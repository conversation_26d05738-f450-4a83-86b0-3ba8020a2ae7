# Blender Commits: 2025 Week 26 (June 23–29)

This file contains ALL commits made to the Blender repository during ISO Week 26 of 2025 (June 23–29). Each entry includes:
- Full commit hash
- Date
- Description
- Author
- Direct link to the commit on GitHub

**Total commits found: 200+**

## Commit List (Chronological Order - Most Recent First)

---

**4bc5874e89674e3fad4ef2190935aec7ebbeb388**
- Date: 2025-06-28
- Description: Refactor: BLO: ID listing in blendfile: factorize 'read & check' logic.
- Author: <PERSON><PERSON><PERSON>
- [View on GitHub](https://github.com/blender/blender/commit/4bc5874e89674e3fad4ef2190935aec7ebbeb388)

**72e5254669b07ec0549a1c3d747aecd52b40f9b1**
- Date: 2024-06-29
- Description: Merge branch 'blender-v4.2-release'
- Author: <PERSON>
- [View on GitHub](https://github.com/blender/blender/commit/72e5254669b07ec0549a1c3d747aecd52b40f9b1)

**87844ae24dd71ea0259084e70768967e5735f14a**
- Date: 2024-06-28
- Description: Fix bl_text_utils/external_editor.py broken __all__
- Author: Andrej730
- [View on GitHub](https://github.com/blender/blender/commit/87844ae24dd71ea0259084e70768967e5735f14a)

**34c325308f49ee6a67f46ed99c4e0bbb9f4e4014**
- Date: 2024-06-29
- Description: Merge branch 'blender-v4.2-release'
- Author: Julien Duroure
- [View on GitHub](https://github.com/blender/blender/commit/34c325308f49ee6a67f46ed99c4e0bbb9f4e4014)

**38237fe414169be139b0718bcf20d08468edf478**
- Date: 2024-06-29
- Description: glTF exporter: Avoid crash with skinned lattices
- Author: Julien Duroure
- [View on GitHub](https://github.com/blender/blender/commit/38237fe414169be139b0718bcf20d08468edf478)

**75c9064a5030f1988b42101bb366fe047b73e202**
- Date: 2024-06-29
- Description: Merge branch 'blender-v4.2-release'
- Author: Julien Duroure
- [View on GitHub](https://github.com/blender/blender/commit/75c9064a5030f1988b42101bb366fe047b73e202)

**77a382ba2ffb214d9f5bd690df492ffd212f39b6**
- Date: 2024-06-29
- Description: glTF exporter: call hook for each action, including the active one
- Author: Julien Duroure
- [View on GitHub](https://github.com/blender/blender/commit/77a382ba2ffb214d9f5bd690df492ffd212f39b6)

**d90675e6621f9fee262f71f43da88f11a2ebb325**
- Date: 2024-06-29
- Description: GPv3: Draw Tool: Write start time and delta time attributes
- Author: Falk David
- [View on GitHub](https://github.com/blender/blender/commit/d90675e6621f9fee262f71f43da88f11a2ebb325)

**4c5c5e2fd7de681bd6db7ce5d8fbe0ac395302f4**
- Date: 2024-06-29
- Description: Fix: `safe_normalize()` not defined for `float2` on Metal
- Author: Weizhen Huang
- [View on GitHub](https://github.com/blender/blender/commit/4c5c5e2fd7de681bd6db7ce5d8fbe0ac395302f4)

**4162aeee5f91ebac9110266ac58188e31ebfa6c6**
- Date: 2024-06-28
- Description: Fix: Sculpt: Incorrect undo behavior with modifiers and new brush code
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/4162aeee5f91ebac9110266ac58188e31ebfa6c6)

**0d6148c514d40776477a91b919d0fe61e7d6ea26**
- Date: 2024-06-28
- Description: Cleanup: Fix assert test in BMesh sculpt brush code
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/0d6148c514d40776477a91b919d0fe61e7d6ea26)

**071479576568622e45006901eac4d5a88b15153a**
- Date: 2024-06-28
- Description: Sculpt: Improve base mesh area/normal sampling performance
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/071479576568622e45006901eac4d5a88b15153a)

**5643132c08cb6bacbbd515ce7790c391d15be5c4**
- Date: 2024-06-28
- Description: Refactor: Sculpt: Specialize area/normal sampling loops per PBVH type
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/5643132c08cb6bacbbd515ce7790c391d15be5c4)

**a28a9607152bc6dc4b0b8a9824a7a8d0fffcad01**
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Richard Antalik
- [View on GitHub](https://github.com/blender/blender/commit/a28a9607152bc6dc4b0b8a9824a7a8d0fffcad01)

**db5cc087141a0ba5f18b5e038e7f24b3e87df436**
- Date: 2024-06-28
- Description: Revert "Fix #118505: Incorrect strip image transformation"
- Author: Richard Antalik
- [View on GitHub](https://github.com/blender/blender/commit/db5cc087141a0ba5f18b5e038e7f24b3e87df436)

**fe54824f24dde74325952d55a314027e8ec2751e**
- Date: 2024-06-28
- Description: Fix and test direction_to_fisheye_lens_polynomial
- Author: Alexander Brock
- [View on GitHub](https://github.com/blender/blender/commit/fe54824f24dde74325952d55a314027e8ec2751e)

**c93767a8b41540109441839f616f9b678e3c7648**
- Date: 2024-06-28
- Description: Sculpt: Extend refactor to change dyntopo in brushes
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/c93767a8b41540109441839f616f9b678e3c7648)

**96018c8e8f0a6f6e38f64cb9135182a06535324a**
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Sergey Sharybin
- [View on GitHub](https://github.com/blender/blender/commit/96018c8e8f0a6f6e38f64cb9135182a06535324a)

**66733028402b1b66c092fc32ec2c16ebdad5b2a9**
- Date: 2024-06-28
- Description: EEVEE: Shadow: Split Tilemap finalize
- Author: Clément Foucault
- [View on GitHub](https://github.com/blender/blender/commit/66733028402b1b66c092fc32ec2c16ebdad5b2a9)

**3f3dfe168033923db07a0d6de16fdb409fd1e675**
- Date: 2024-06-28
- Description: Cleanup: fix unused parameter compiler warning in release builds
- Author: Nathan Vegdahl
- [View on GitHub](https://github.com/blender/blender/commit/3f3dfe168033923db07a0d6de16fdb409fd1e675)

**c8b736659ba7414d63af9893f2c5b12745c29125**
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Richard Antalik
- [View on GitHub](https://github.com/blender/blender/commit/c8b736659ba7414d63af9893f2c5b12745c29125)

**3d172170255d0bfc349c386393f98d9fe4b34511**
- Date: 2024-06-28
- Description: Fix #118505: Incorrect strip image transformation
- Author: Richard Antalik
- [View on GitHub](https://github.com/blender/blender/commit/3d172170255d0bfc349c386393f98d9fe4b34511)

**1a887c06d95a2164c66e9de7c65c0a24ab18a443**
- Date: 2024-06-28
- Description: Fix #123543: Delete retiming keys deletes strip
- Author: Richard Antalik
- [View on GitHub](https://github.com/blender/blender/commit/1a887c06d95a2164c66e9de7c65c0a24ab18a443)

**2362d909fc2315cb32a2c92e0b08cc590866af11**
- Date: 2024-06-28
- Description: UI: Avoid redundant view item button lookup in context menu code
- Author: Julian Eisel
- [View on GitHub](https://github.com/blender/blender/commit/2362d909fc2315cb32a2c92e0b08cc590866af11)

**c7e75090bd7e971db9edb43860e31e688f665368**
- Date: 2024-06-28
- Description: Fix #115981: Edge panning causes strips to overlap
- Author: Richard Antalik
- [View on GitHub](https://github.com/blender/blender/commit/c7e75090bd7e971db9edb43860e31e688f665368)

**851505752ffc2b5677e836f44afbf0b2f948fb55**
- Date: 2024-06-28
- Description: Sculpt: Extend refactor to change multires in brushes
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/851505752ffc2b5677e836f44afbf0b2f948fb55)

**cc1070de2dafc83b2a9684ee9e981facb73f4186**
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Sergey Sharybin
- [View on GitHub](https://github.com/blender/blender/commit/cc1070de2dafc83b2a9684ee9e981facb73f4186)

**c309479912eafbe1bb2165f8e4eea4325d577750**
- Date: 2024-06-28
- Description: Fix: Changing sculpt multires level erases sculpt changes
- Author: Sergey Sharybin
- [View on GitHub](https://github.com/blender/blender/commit/c309479912eafbe1bb2165f8e4eea4325d577750)

**8fe9c5ee04c5a0b4608f9a1abb06c8fa6a89e668**
- Date: 2024-06-28
- Description: Cleanup: run clang format
- Author: Nathan Vegdahl
- [View on GitHub](https://github.com/blender/blender/commit/8fe9c5ee04c5a0b4608f9a1abb06c8fa6a89e668)

**4ebcd7e1d1b0ff1cf2699204783c5f60a9c4fadd**
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Omar Emara
- [View on GitHub](https://github.com/blender/blender/commit/4ebcd7e1d1b0ff1cf2699204783c5f60a9c4fadd)

**2c345cfe31adba0492cfd136b4628acbb36e0ce2**
- Date: 2024-06-28
- Description: Fix: Compositor frees cached resources when canceled
- Author: Omar Emara
- [View on GitHub](https://github.com/blender/blender/commit/2c345cfe31adba0492cfd136b4628acbb36e0ce2)

**2eb7c4c3299c543d74f4e28f8de064639ce51e32**
- Date: 2024-06-28
- Description: Fix: Linked images don't work in GPU compositor
- Author: Omar Emara
- [View on GitHub](https://github.com/blender/blender/commit/2eb7c4c3299c543d74f4e28f8de064639ce51e32)

**f187e84fe37367c4e262f782227e95affe980b69**
- Date: 2024-06-28
- Description: Cleanup: Sculpt: Remove unused variables from PBVH draw data
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/f187e84fe37367c4e262f782227e95affe980b69)

**924942faddac85e866ef5aa641c75ab2f7ca7db4**
- Date: 2024-06-28
- Description: Cleanup: Resolve warnings after previous color painting commit
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/924942faddac85e866ef5aa641c75ab2f7ca7db4)

**7f2d0f7b1461454e33b3e9326efaab2490b6224b**
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/7f2d0f7b1461454e33b3e9326efaab2490b6224b)

**b62afb8cd4fb44e03263bfbf9ccb31a1c4a8dfff**
- Date: 2024-06-28
- Description: Unbreak build in sculpt_paint_color.cc
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/b62afb8cd4fb44e03263bfbf9ccb31a1c4a8dfff)

**a90a7cd521b1233974ce439a768e5457e00ea02a**
- Date: 2024-06-28
- Description: Refactor: Sculpt: Consolidate color painting API
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/a90a7cd521b1233974ce439a768e5457e00ea02a)

**acfa60c1ae09bdf9f4fc0c327632b8c669bf5a1b**
- Date: 2024-06-28
- Description: Cleanup: rename misleading function name, quiet mypy warning
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/acfa60c1ae09bdf9f4fc0c327632b8c669bf5a1b)

**155b7d682213586b45d33ccff528916301d8cce3**
- Date: 2024-06-28
- Description: Refactor: Sculpt: Remove color attributes from PBVH and SculptSession
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/155b7d682213586b45d33ccff528916301d8cce3)

**fba79e21dd7f1a707170e94d6c46c43513c49c78**
- Date: 2024-06-27
- Description: Refactor: Sculpt: Replace abstract vertex abstraction for color painting
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/fba79e21dd7f1a707170e94d6c46c43513c49c78)

**5427775fefc21dccf694a9b51377eb03452f974c**
- Date: 2024-06-28
- Description: Anim: Theme entry for time visualization
- Author: Christoph Lendenfeld
- [View on GitHub](https://github.com/blender/blender/commit/5427775fefc21dccf694a9b51377eb03452f974c)

**c4a7c4e2a1d755e6c753d146d0ea58ac7e9c575d**
- Date: 2024-06-13
- Description: Anim: add `ID*` cache of users to Action Bindings
- Author: Sybren A. Stüvel
- [View on GitHub](https://github.com/blender/blender/commit/c4a7c4e2a1d755e6c753d146d0ea58ac7e9c575d)

**d342f64fa4011168b72adb182065d554559ba97f**
- Date: 2024-06-28
- Description: Core: add `BKE_lib_query_foreachid_process_main_get()` function
- Author: Sybren A. Stüvel
- [View on GitHub](https://github.com/blender/blender/commit/d342f64fa4011168b72adb182065d554559ba97f)

**d3aa4ecfe6115e2f25eff22bafd1683aa5213b8a**
- Date: 2024-06-28
- Description: Core: pass `bmain` to `BKE_library_foreach_ID_link()` wherever possible
- Author: Sybren A. Stüvel
- [View on GitHub](https://github.com/blender/blender/commit/d3aa4ecfe6115e2f25eff22bafd1683aa5213b8a)

**74c4b41f1a4066253c06d9cbab8d35559d4102b5**
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Philipp Oeser
- [View on GitHub](https://github.com/blender/blender/commit/74c4b41f1a4066253c06d9cbab8d35559d4102b5)

**33ca4daf8f6de521a728e40ec02df85dc9eece38**
- Date: 2024-06-28
- Description: Fix #123882: Ocean Modifier not updating normals in "Displace" mode
- Author: Philipp Oeser
- [View on GitHub](https://github.com/blender/blender/commit/33ca4daf8f6de521a728e40ec02df85dc9eece38)

**5b444f59548e15832986fe8848ef82c2613a6341**
- Date: 2024-06-28
- Description: Fix: GPv3: Use `OPTYPE_UNDO` for color tag operator
- Author: Pratik Borhade
- [View on GitHub](https://github.com/blender/blender/commit/5b444f59548e15832986fe8848ef82c2613a6341)

**dc72e5bac96164f469ce5b90dda662fa1a78c16d**
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Philipp Oeser
- [View on GitHub](https://github.com/blender/blender/commit/dc72e5bac96164f469ce5b90dda662fa1a78c16d)

**296d05060d22714a5fe46af7206aa7066982e9c8**
- Date: 2024-06-28
- Description: Fix #123560: Select by active material fails in multi-object-edit mode
- Author: Philipp Oeser
- [View on GitHub](https://github.com/blender/blender/commit/296d05060d22714a5fe46af7206aa7066982e9c8)

**343426087495283db662dd60308ea1b9c52a6fe3**
- Date: 2024-06-28
- Description: Vulkan: Add support for Cycles CPU
- Author: Jeroen Bakker
- [View on GitHub](https://github.com/blender/blender/commit/343426087495283db662dd60308ea1b9c52a6fe3)

**da4746fe96e3fb0b028e2d7bf529867d49250582**
- Date: 2024-06-28
- Description: Vulkan: Fix copy depth images with stencil
- Author: Jeroen Bakker
- [View on GitHub](https://github.com/blender/blender/commit/da4746fe96e3fb0b028e2d7bf529867d49250582)

**eb35212f3df7a755e0afb48df1601a5f7e613279**
- Date: 2024-06-28
- Description: Cleanup: make format
- Author: Jacques Lucke
- [View on GitHub](https://github.com/blender/blender/commit/eb35212f3df7a755e0afb48df1601a5f7e613279)

**3410d0bf3ff729b2009a0b0b14cf5cbddc6214ac**
- Date: 2024-06-28
- Description: Nodes: simplify node link drawing shader
- Author: Jacques Lucke
- [View on GitHub](https://github.com/blender/blender/commit/3410d0bf3ff729b2009a0b0b14cf5cbddc6214ac)

**91bbb27f8c1fe65ebbd3d73db2d14a0268320862**
- Date: 2024-06-28
- Description: Fix: GPv3: Object line art not working
- Author: Pratik Borhade
- [View on GitHub](https://github.com/blender/blender/commit/91bbb27f8c1fe65ebbd3d73db2d14a0268320862)

**43c3ab9acb116a5800b99311d9b2200d315cbed6**
- Date: 2024-06-28
- Description: Geometry Nodes: refactor multi-input handling in lazy-function graph generation
- Author: Jacques Lucke
- [View on GitHub](https://github.com/blender/blender/commit/43c3ab9acb116a5800b99311d9b2200d315cbed6)

**dda4a50bf596a6a2cfab5da3e5eb60e52258f006**
- Date: 2024-06-28
- Description: Geometry Nodes: add utility to access logged primitive values
- Author: Jacques Lucke
- [View on GitHub](https://github.com/blender/blender/commit/dda4a50bf596a6a2cfab5da3e5eb60e52258f006)

**d367b6e4a29ff88834bf750e2065919534df99ee**
- Date: 2024-06-28
- Description: Nodes: add non-const owner_tree access
- Author: Jacques Lucke
- [View on GitHub](https://github.com/blender/blender/commit/d367b6e4a29ff88834bf750e2065919534df99ee)

**d819bed88fcbd608ae757e58f94930d0e1ab0ed9**
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Jacques Lucke
- [View on GitHub](https://github.com/blender/blender/commit/d819bed88fcbd608ae757e58f94930d0e1ab0ed9)

**d6bf027f38a7816708283425d2f1cc9f0e28131f**
- Date: 2024-06-28
- Description: Fix: properly handle negative scale in safe conversion to loc/rot/scale
- Author: Jacques Lucke
- [View on GitHub](https://github.com/blender/blender/commit/d6bf027f38a7816708283425d2f1cc9f0e28131f)

**7866fcd8699d717ada773d161d60ce7b369e2687**
- Date: 2024-06-28
- Description: Fix: convert math::Axis to vector
- Author: Jacques Lucke
- [View on GitHub](https://github.com/blender/blender/commit/7866fcd8699d717ada773d161d60ce7b369e2687)

**b59e009acc4562b681560bf3f48f0a7268c38c2b**
- Date: 2024-06-28
- Description: UI: Add function to query debug name of view items
- Author: Julian Eisel
- [View on GitHub](https://github.com/blender/blender/commit/b59e009acc4562b681560bf3f48f0a7268c38c2b)

**42e66f2912eac15f6fe07cb93cbe423bb85b40c9**
- Date: 2024-06-28
- Description: Merge remote-tracking branch 'origin/blender-v4.2-release'
- Author: Dalai Felinto
- [View on GitHub](https://github.com/blender/blender/commit/42e66f2912eac15f6fe07cb93cbe423bb85b40c9)

**b528aca71198ca042e92ecab2c84306cd4bdf540**
- Date: 2024-06-28
- Description: Merge remote-tracking branch 'origin/blender-v4.2-release'
- Author: Dalai Felinto
- [View on GitHub](https://github.com/blender/blender/commit/b528aca71198ca042e92ecab2c84306cd4bdf540)

**4bff6ba6558b204028b416483fcfb845abaa150b**
- Date: 2024-06-28
- Description: Remove Rigify from extensions_map_from_legacy_addons.py
- Author: Sybren A. Stüvel
- [View on GitHub](https://github.com/blender/blender/commit/4bff6ba6558b204028b416483fcfb845abaa150b)

**246b317bd4f5cb92fb40eae03fd1d0a27d087070**
- Date: 2024-06-27
- Description: Add-on: Rigify, move meta-rigs into a 'Rigify Meta-Rigs' sub-menu
- Author: Sybren A. Stüvel
- [View on GitHub](https://github.com/blender/blender/commit/246b317bd4f5cb92fb40eae03fd1d0a27d087070)

**d1e7346c630f8bf63e96a0a7e417acd1fbd425f3**
- Date: 2024-06-27
- Description: Add-ons: Rigify, reformat code
- Author: Sybren A. Stüvel
- [View on GitHub](https://github.com/blender/blender/commit/d1e7346c630f8bf63e96a0a7e417acd1fbd425f3)

**09e431c511a132f8bf39eb1abecbdcea077a4a84**
- Date: 2024-06-27
- Description: Rigify: mark as support=OFFICIAL
- Author: Sybren A. Stüvel
- [View on GitHub](https://github.com/blender/blender/commit/09e431c511a132f8bf39eb1abecbdcea077a4a84)

**3ad753e9f49a50efa903789486d68398da811ab9**
- Date: 2024-06-18
- Description: Fix: make foot roll rigs work again in Blender 4.2 and later (#4)
- Author: Nathan Vegdahl
- [View on GitHub](https://github.com/blender/blender/commit/3ad753e9f49a50efa903789486d68398da811ab9)

**8d4cc5d99882ef924bfbd36a4ea19877850393aa**
- Date: 2024-06-07
- Description: Remove CloudRig as a promoted feature set (#1)
- Author: Demeter Dzadik
- [View on GitHub](https://github.com/blender/blender/commit/8d4cc5d99882ef924bfbd36a4ea19877850393aa)

**f5ac944658dec72971798ccfa8ce60659be912dd**
- Date: 2024-06-04
- Description: Fix: Rigify Apply Toggle Pole to Keyframes clears out IK keyframes
- Author: Alexander Gavrilov
- [View on GitHub](https://github.com/blender/blender/commit/f5ac944658dec72971798ccfa8ce60659be912dd)

**541e3aae25b0b01012c10165acb0b5c1308429a7**
- Date: 2024-06-27
- Description: Add-ons: Move Rigify into addons_core
- Author: Sybren A. Stüvel
- [View on GitHub](https://github.com/blender/blender/commit/541e3aae25b0b01012c10165acb0b5c1308429a7)

**270c4fad48cf11d4a47f0394e27af0d34fc89dbf**
- Date: 2024-06-28
- Description: Fix #123876: Crash using eyedropper tool
- Author: Falk David
- [View on GitHub](https://github.com/blender/blender/commit/270c4fad48cf11d4a47f0394e27af0d34fc89dbf)

**51e68fe0ed620c21843f4e92cb266f470787bec0**
- Date: 2024-06-28
- Description: Cleanup: make format
- Author: Dalai Felinto
- [View on GitHub](https://github.com/blender/blender/commit/51e68fe0ed620c21843f4e92cb266f470787bec0)

**84f11da63ab1d202e70a3502758bdc6cbe0936bc**
- Date: 2024-06-28
- Description: UI: Extensions: Fix spacing between Install and Menu
- Author: Dalai Felinto
- [View on GitHub](https://github.com/blender/blender/commit/84f11da63ab1d202e70a3502758bdc6cbe0936bc)

**c83727f0bd71f6a965a7599874fca1acd20d9f9c**
- Date: 2024-06-28
- Description: Anim: add functions for asserting Project Baklava phase-1 invariants
- Author: Nathan Vegdahl
- [View on GitHub](https://github.com/blender/blender/commit/c83727f0bd71f6a965a7599874fca1acd20d9f9c)

**92c026c39ba9cfdc92f5a463a6aca963d20d7a22**
- Date: 2024-06-24
- Description: I18n: Fix multi-context message extraction regex
- Author: Damien Picard
- [View on GitHub](https://github.com/blender/blender/commit/92c026c39ba9cfdc92f5a463a6aca963d20d7a22)

**cae1faec12e9498ba0453bf43d35e572909a7802**
- Date: 2024-06-28
- Description: Refactor: bundle fcurve lookup/creation parameters in a struct
- Author: Nathan Vegdahl
- [View on GitHub](https://github.com/blender/blender/commit/cae1faec12e9498ba0453bf43d35e572909a7802)

**bea0c5c914033a091be2059282326567c8ace49b**
- Date: 2024-06-28
- Description: UI: Small Modifications to Some Icons
- Author: Harley Acheson
- [View on GitHub](https://github.com/blender/blender/commit/bea0c5c914033a091be2059282326567c8ace49b)

**ac8da6c72ed22de1533e95e11da96077b81df2ac**
- Date: 2024-06-28
- Description: Extensions: move junction_module to a private location
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/ac8da6c72ed22de1533e95e11da96077b81df2ac)

**549d6fb573f6a059b09877748cb55edcae88984c**
- Date: 2024-06-28
- Description: Cleanup: Sculpt: Add assert on span size for helper method
- Author: Sean Kim
- [View on GitHub](https://github.com/blender/blender/commit/549d6fb573f6a059b09877748cb55edcae88984c)

**d0a3d629b9264940b7ea42fd9c2ff1807d62fb39**
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/d0a3d629b9264940b7ea42fd9c2ff1807d62fb39)

**80f07e3f26747eb9c68d50abd36851607a3e4312**
- Date: 2024-06-28
- Description: Cleanup: simplify the tags drawing function
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/80f07e3f26747eb9c68d50abd36851607a3e4312)

**dbe3956c76d4c8e180ac6ac4fb7074de4d941a07**
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/dbe3956c76d4c8e180ac6ac4fb7074de4d941a07)

**1043b273d59e1d40b52ee9ca92ce76ecfe1eae76**
- Date: 2024-06-28
- Description: Extensions: hide the extensions add-on unless debugging extensions
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/1043b273d59e1d40b52ee9ca92ce76ecfe1eae76)

**2fd7db06333ec6ce293c4f61d9f3ddc6d0dc54a2**
- Date: 2024-06-28
- Description: Extensions: hide the extensions add-on unless debugging extensions
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/2fd7db06333ec6ce293c4f61d9f3ddc6d0dc54a2)

**60a5ef492c06bedbf470e07dcac5b559f3cb69bc**
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/60a5ef492c06bedbf470e07dcac5b559f3cb69bc)

**37ae9d5fc47e2842ee77a07482039d24a06784f9**
- Date: 2024-06-28
- Description: Fix #123827: Extension cannot be uninstalled if symlinked
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/37ae9d5fc47e2842ee77a07482039d24a06784f9)

**4ef2381ce3e6418cc18912f61d599769d4f27c98**
- Date: 2024-06-28
- Description: Cleanup: UI: Remove Unused Icon Texture Drawing Code
- Author: Harley Acheson
- [View on GitHub](https://github.com/blender/blender/commit/4ef2381ce3e6418cc18912f61d599769d4f27c98)

**0167dd08fc140307c0b148ffa6a593595d06c80c**
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/0167dd08fc140307c0b148ffa6a593595d06c80c)

**9d10b88f376b6cca88c834cbe901d4441c55c970**
- Date: 2024-06-28
- Description: Cleanup: remove unnecessary icon scanning on startup
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/9d10b88f376b6cca88c834cbe901d4441c55c970)

**d77ebc41de80a806e80f489558f119c684f4e43f**
- Date: 2024-06-28
- Description: Cleanup: remove icon file lists & unused functions
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/d77ebc41de80a806e80f489558f119c684f4e43f)

**1ede471ba2b5db775df4cda538246e080169b4e8**
- Date: 2024-06-27
- Description: Cleanup: Remove unnecessary namespaces, pass math types by value
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/1ede471ba2b5db775df4cda538246e080169b4e8)

**868fed96c9290efdb2ea30c6220e38c81ee0930c**
- Date: 2024-06-27
- Description: Cleanup: Sculpt: Remove unused sculpt clay brush code
- Author: Sean Kim
- [View on GitHub](https://github.com/blender/blender/commit/868fed96c9290efdb2ea30c6220e38c81ee0930c)

**4d84940253f556371dee2505d12d231506cd61bc**
- Date: 2024-06-27
- Description: UI: File Browser File Type Icons Use Shader Outline
- Author: Harley Acheson
- [View on GitHub](https://github.com/blender/blender/commit/4d84940253f556371dee2505d12d231506cd61bc)

**696725590665dffa917e8508fba8cb77e0553e00**
- Date: 2024-06-27
- Description: Color management: Support white balance as part of the display transform
- Author: Lukas Stockner
- [View on GitHub](https://github.com/blender/blender/commit/696725590665dffa917e8508fba8cb77e0553e00)

**9ae237d0b48423913d28a93eb4be6fe4a3c32dcd**
- Date: 2024-06-27
- Description: UI: Allow Discretionary Use of Icon Outline
- Author: Harley Acheson
- [View on GitHub](https://github.com/blender/blender/commit/9ae237d0b48423913d28a93eb4be6fe4a3c32dcd)

**5c7821fd79318edd8997105c91d5c55059883e4e**
- Date: 2024-06-27
- Description: imbuf: Add regression test for PSD
- Author: Jesse Yurkovich
- [View on GitHub](https://github.com/blender/blender/commit/5c7821fd79318edd8997105c91d5c55059883e4e)

**45c03764bf7c538f891a58e51e2d7e395d8d56da**
- Date: 2024-06-27
- Description: imbuf: Add PSD test files
- Author: Jesse Yurkovich
- [View on GitHub](https://github.com/blender/blender/commit/45c03764bf7c538f891a58e51e2d7e395d8d56da)

**8f64329c79501bbea8a86d280fbdc20bbbebfa63**
- Date: 2024-06-27
- Description: Refactor: Use newer attribute API in `create_liquid_geometry`
- Author: Falk David
- [View on GitHub](https://github.com/blender/blender/commit/8f64329c79501bbea8a86d280fbdc20bbbebfa63)

**d16eac9ceeef1acb0fbd4d44cc06b3fdc9fe7d0b**
- Date: 2024-06-27
- Description: Merge branch 'blender-v4.2-release'
- Author: Sergey Sharybin
- [View on GitHub](https://github.com/blender/blender/commit/d16eac9ceeef1acb0fbd4d44cc06b3fdc9fe7d0b)

**9e4d9295f0fb299773324be6125c1d3b783ebef4**
- Date: 2024-06-27
- Description: Fix: Compilation error of fribidi on macOS
- Author: Sergey Sharybin
- [View on GitHub](https://github.com/blender/blender/commit/9e4d9295f0fb299773324be6125c1d3b783ebef4)

**e1ca54c52a8b1a9e34afb870c4825e83164a8d9b**
- Date: 2024-06-27
- Description: Fix #123768: Sculpt: Crash on undo with multires
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/e1ca54c52a8b1a9e34afb870c4825e83164a8d9b)

**39fe42fcac718cb99e75aff920f5e7c00f7b068d**
- Date: 2024-06-27
- Description: Windows: 4.2 Library incremental (OIIO+OIDN)
- Author: Anthony Roberts
- [View on GitHub](https://github.com/blender/blender/commit/39fe42fcac718cb99e75aff920f5e7c00f7b068d)

**a0f0a4dd49f7d4a4473f7dc68c0941a6ca5f8cfd**
- Date: 2024-06-27
- Description: Cleanup: Remove `BKE_attribute_allow_procedural_access`
- Author: Falk David
- [View on GitHub](https://github.com/blender/blender/commit/a0f0a4dd49f7d4a4473f7dc68c0941a6ca5f8cfd)

**baf07c22bdbb33f0ecf13aa483f5fb3fa8a486e4**
- Date: 2024-06-27
- Description: Revert "Fix #123794: Crash when UDIMs have gray and color tiles"
- Author: Miguel Pozo
- [View on GitHub](https://github.com/blender/blender/commit/baf07c22bdbb33f0ecf13aa483f5fb3fa8a486e4)

**095e78bd28bbbab98bc66a8d8947ab1bfae969ac**
- Date: 2024-06-27
- Description: Fix #123794: Crash when UDIMs have gray and color tiles
- Author: Miguel Pozo
- [View on GitHub](https://github.com/blender/blender/commit/095e78bd28bbbab98bc66a8d8947ab1bfae969ac)

**b2f8f5a491f639e6207a8106da50b43d35f1f006**
- Date: 2024-06-27
- Description: Fix: USD import: domelight Y-up orientation
- Author: Michael Kowalski
- [View on GitHub](https://github.com/blender/blender/commit/b2f8f5a491f639e6207a8106da50b43d35f1f006)

**e75dfaf17b22795d55187b17dac0d483a3aa8ffe**
- Date: 2024-06-27
- Description: Merge branch 'blender-v4.2-release'
- Author: Sergey Sharybin
- [View on GitHub](https://github.com/blender/blender/commit/e75dfaf17b22795d55187b17dac0d483a3aa8ffe)

**32588169d3ff2177a9e78ff89ba3255830533b76**
- Date: 2024-06-27
- Description: Fix: Initialization of paint mode fails in certain files
- Author: Sergey Sharybin
- [View on GitHub](https://github.com/blender/blender/commit/32588169d3ff2177a9e78ff89ba3255830533b76)

**a8fae77f10306584db3b62127defc618481c17cd**
- Date: 2024-06-27
- Description: Cleanup: remove unused icon utilities and make convenience target
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/a8fae77f10306584db3b62127defc618481c17cd)

**9584f8fb550cbe6b3cdd3048925617be0634442b**
- Date: 2024-06-27
- Description: Cleanup: Sculpt: Fix misleading naming of grids index buffer functions
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/9584f8fb550cbe6b3cdd3048925617be0634442b)

**dc70f454b7ea2a268ef4f03ee8c4ae746c18a132**
- Date: 2024-06-27
- Description: Cleanup: Sculpt: Extract vertex buffer filling into separate functions
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/dc70f454b7ea2a268ef4f03ee8c4ae746c18a132)

**1453136ad0620e8f86aa20375d2cd239683d4465**
- Date: 2024-06-27
- Description: Cleanup: Sculpt: Simplify drawing visible face counting
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/1453136ad0620e8f86aa20375d2cd239683d4465)

**98939c29d4e431eb6db070f333021a817ff8ec80**
- Date: 2024-06-27
- Description: Merge branch 'blender-v4.2-release'
- Author: Christoph Lendenfeld
- [View on GitHub](https://github.com/blender/blender/commit/98939c29d4e431eb6db070f333021a817ff8ec80)

**f3b393a74ac26898f491acd9953dcd331928eda4**
- Date: 2024-06-27
- Description: Fix #123738: Keyframe drawing issue while duplicating keys
- Author: Christoph Lendenfeld
- [View on GitHub](https://github.com/blender/blender/commit/f3b393a74ac26898f491acd9953dcd331928eda4)

**e72e538fdd051249edc65a9ba2941a17337dff6d**
- Date: 2024-06-27
- Description: Vulkan: Fix sequential read hazard
- Author: Jeroen Bakker
- [View on GitHub](https://github.com/blender/blender/commit/e72e538fdd051249edc65a9ba2941a17337dff6d)

**15ef372567885e70a7ebd2e64b17cddcd7d2d998**
- Date: 2024-06-27
- Description: Merge branch 'blender-v4.2-release'
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/15ef372567885e70a7ebd2e64b17cddcd7d2d998)

**8945b7e49af7fce1dd6166fe6163c98f9c98b8c3**
- Date: 2024-06-27
- Description: Fix #123809: Sculpt visibility invert missing PBVH node update
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/8945b7e49af7fce1dd6166fe6163c98f9c98b8c3)

**ef8f14f3d6114ee6e5cfa516350fb58b3bcf2c12**
- Date: 2024-06-27
- Description: Fix #90923: Bone Stick active color
- Author: Christoph Lendenfeld
- [View on GitHub](https://github.com/blender/blender/commit/ef8f14f3d6114ee6e5cfa516350fb58b3bcf2c12)

**adfe6880464b97cef250c583b89c4059cd6029af**
- Date: 2024-06-27
- Description: Fix another batch of mismatches `MEM_new`/`MEM_freeN` cases in UI/Assets code.
- Author: Bastien Montagne
- [View on GitHub](https://github.com/blender/blender/commit/adfe6880464b97cef250c583b89c4059cd6029af)

**643334b7278ebbc47697e7935b2921b58682a31d**
- Date: 2024-06-26
- Description: Sculpt: Resolve over-allocation of multires draw vertex buffers
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/643334b7278ebbc47697e7935b2921b58682a31d)

**83a15be1099786063a179e8aad62c8a0f82f1f3f**
- Date: 2024-06-26
- Description: Sculpt: Improve multires drawing performance by simplifying logic
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/83a15be1099786063a179e8aad62c8a0f82f1f3f)

**7cf9854938d3d1bf8156bbf05cb8375e25522e98**
- Date: 2024-06-26
- Description: Sculpt: Avoid vertex buffer access overhead for multires drawing
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/7cf9854938d3d1bf8156bbf05cb8375e25522e98)

**b013eed96723fe0b982403981c431dc9edc46b49**
- Date: 2024-06-26
- Description: Sculpt: Remove double function call indirection in multires drawing
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/b013eed96723fe0b982403981c431dc9edc46b49)

**3009c98d5f1acef3c34d51adcf6068ca4b427e13**
- Date: 2024-06-27
- Description: Vulkan: Fix read-after-write hazard in draw manager visibility
- Author: Jeroen Bakker
- [View on GitHub](https://github.com/blender/blender/commit/3009c98d5f1acef3c34d51adcf6068ca4b427e13)

**fb2e712991d5720ec90b2bb8c7da5fe6cabe2180**
- Date: 2024-06-27
- Description: VSE: Clarify wording for Replace Selection property
- Author: Richard Antalik
- [View on GitHub](https://github.com/blender/blender/commit/fb2e712991d5720ec90b2bb8c7da5fe6cabe2180)

**cb68d0c9d73a9f51c72ca076be18f67eba9d593d**
- Date: 2024-06-27
- Description: Merge branch 'blender-v4.2-release'
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/cb68d0c9d73a9f51c72ca076be18f67eba9d593d)

**1553a75feddb32546ae02074cc86ebb3fa15a2b2**
- Date: 2024-06-27
- Description: Fix regression installing an addon by drag'n'drop
- Author: Andrej730
- [View on GitHub](https://github.com/blender/blender/commit/1553a75feddb32546ae02074cc86ebb3fa15a2b2)

**84e5de33278fee57bb88ec18f43493b07daff703**
- Date: 2024-06-27
- Description: Fix: GPv3: Crash in `grease_pencil_object_cache_populate`
- Author: Falk David
- [View on GitHub](https://github.com/blender/blender/commit/84e5de33278fee57bb88ec18f43493b07daff703)

**8e2cbdc876c061840c114d8afa5e990b3e0552b2**
- Date: 2024-06-27
- Description: Merge branch 'blender-v4.2-release'
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/8e2cbdc876c061840c114d8afa5e990b3e0552b2)

**c3d18854f381ccb4d2a770cc212eabe746eea219**
- Date: 2024-06-27
- Description: Cleanup: avoid unnecessary separator
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/c3d18854f381ccb4d2a770cc212eabe746eea219)

**6f06e2258f920bc3c224aa4d33023c248b9236cb**
- Date: 2024-06-27
- Description: Image: Clarify color depth tooltip for EXR images
- Author: Omar Emara
- [View on GitHub](https://github.com/blender/blender/commit/6f06e2258f920bc3c224aa4d33023c248b9236cb)

**f1bfaaf2f781c50d31ee55e4a6bb262a08752de7**
- Date: 2024-06-27
- Description: Merge branch 'blender-v4.2-release'
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/f1bfaaf2f781c50d31ee55e4a6bb262a08752de7)

**fb94028d10cf013ad5030df88f398aaad84d872a**
- Date: 2024-06-27
- Description: Merge branch 'blender-v4.2-release'
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/fb94028d10cf013ad5030df88f398aaad84d872a)

**e9cba0e588723c12bd0bed06528e6403db153154**
- Date: 2024-06-27
- Description: Fix #123670: EEVEE: Add support for new grease pencil type
- Author: Jeroen Bakker
- [View on GitHub](https://github.com/blender/blender/commit/e9cba0e588723c12bd0bed06528e6403db153154)

**42e1239ba89c9e810855e50886da588a128bb4d6**
- Date: 2024-06-27
- Description: Core: support restricting the types an XML preset may load
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/42e1239ba89c9e810855e50886da588a128bb4d6)

**65d0f365a96a67a7211718f831b1b625391ca763**
- Date: 2024-06-27
- Description: Cleanup: correct misleading name of internal function
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/65d0f365a96a67a7211718f831b1b625391ca763)

**cb9060ede69dfaefb34676960b8d0b013e534ff7**
- Date: 2024-06-27
- Description: Merge branch 'blender-v4.2-release'
- Author: Sergey Sharybin
- [View on GitHub](https://github.com/blender/blender/commit/cb9060ede69dfaefb34676960b8d0b013e534ff7)

**2dc4bd3cdf5eb4a090d738fcdb654b6bfd912625**
- Date: 2024-06-27
- Description: Fix: PSD images are read wrong
- Author: Sergey Sharybin
- [View on GitHub](https://github.com/blender/blender/commit/2dc4bd3cdf5eb4a090d738fcdb654b6bfd912625)

**6aa6aee2d5d8adc726e21cb7019644b0fc219258**
- Date: 2024-06-27
- Description: Reapply "Fix (unreported) Assets: MEM_new/MEM_freeN mismatch usages."
- Author: Bastien Montagne
- [View on GitHub](https://github.com/blender/blender/commit/6aa6aee2d5d8adc726e21cb7019644b0fc219258)

**c6e452d865a09a9d13613bd3c548824bf0585191**
- Date: 2024-06-27
- Description: UI: Extensions: Add a separator between Install and the "⌄" button
- Author: Dalai Felinto
- [View on GitHub](https://github.com/blender/blender/commit/c6e452d865a09a9d13613bd3c548824bf0585191)

**6320066a8803a561c52decc8af6fd90d3243c221**
- Date: 2024-06-27
- Description: Merge branch 'blender-v4.2-release'
- Author: Brecht Van Lommel
- [View on GitHub](https://github.com/blender/blender/commit/6320066a8803a561c52decc8af6fd90d3243c221)

**b802f146e601a776dc053cd9d7d90b887a276ce8**
- Date: 2024-06-27
- Description: Tools: Move warning about wrong clang-format version to the bottom
- Author: Brecht Van Lommel
- [View on GitHub](https://github.com/blender/blender/commit/b802f146e601a776dc053cd9d7d90b887a276ce8)

**cb76781be74320e2883b66de053dcb97e79ea169**
- Date: 2024-06-27
- Description: Revert "Fix (unreported) Assets: MEM_new/MEM_freeN mismatch usages."
- Author: Bastien Montagne
- [View on GitHub](https://github.com/blender/blender/commit/cb76781be74320e2883b66de053dcb97e79ea169)

**77f874ae1dd31f49a9563c7f8e4a4013e54dddc1**
- Date: 2024-06-27
- Description: Fix #123782: Asset Browser does not show tags for active asset
- Author: Philipp Oeser
- [View on GitHub](https://github.com/blender/blender/commit/77f874ae1dd31f49a9563c7f8e4a4013e54dddc1)

**da05bff96c2209c9acd3fa3255ed001198d00cc3**
- Date: 2024-06-27
- Description: Fix (unreported) Assets: MEM_new/MEM_freeN mismatch usages.
- Author: Bastien Montagne
- [View on GitHub](https://github.com/blender/blender/commit/da05bff96c2209c9acd3fa3255ed001198d00cc3)

**f43cf3968918ab6c2deee2fb2e9474c5c2b8a98e**
- Date: 2024-06-27
- Description: Merge branch 'blender-v4.2-release'
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/f43cf3968918ab6c2deee2fb2e9474c5c2b8a98e)

**989de85cf6311d8d6433d1fcda42bfeda1c1b320**
- Date: 2024-06-27
- Description: Extensions: fixed & refactor internals for extension visibility
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/989de85cf6311d8d6433d1fcda42bfeda1c1b320)

**99c75beabfc00c1d9b62e0ef49ff501247def58c**
- Date: 2024-06-27
- Description: Cleanup: Silence unused variable warning
- Author: Sean Kim
- [View on GitHub](https://github.com/blender/blender/commit/99c75beabfc00c1d9b62e0ef49ff501247def58c)

**11d0a9db1b1f3fdd8a9be745dab074cacf10737e**
- Date: 2024-06-27
- Description: Fix: Build error from missing change in recent commit
- Author: Sean Kim
- [View on GitHub](https://github.com/blender/blender/commit/11d0a9db1b1f3fdd8a9be745dab074cacf10737e)

**1a392104c3bfec1ddfa1f7ef8fe3081bd5409432**
- Date: 2024-06-27
- Description: Cleanup: Sculpt: Add BLI_NOINLINE to mask.cc helper methods
- Author: Sean Kim
- [View on GitHub](https://github.com/blender/blender/commit/1a392104c3bfec1ddfa1f7ef8fe3081bd5409432)

**0bc7631693f6f454db93acfb8fa805b8d4d5c3b5**
- Date: 2024-06-27
- Description: Sculpt: Initial data-oriented refactor for clay brush
- Author: Sean Kim
- [View on GitHub](https://github.com/blender/blender/commit/0bc7631693f6f454db93acfb8fa805b8d4d5c3b5)

**6b7255b978bdf109b4f5cdbb379593ce792f7160**
- Date: 2024-06-27
- Description: Refactor: Sculpt: Use return value instead of reference for orig_data
- Author: Sean Kim
- [View on GitHub](https://github.com/blender/blender/commit/6b7255b978bdf109b4f5cdbb379593ce792f7160)

**eed83b56d2799a2a362f417aa7c2afb9eb450be4**
- Date: 2024-06-27
- Description: UI: Calm Warnings With New Icons
- Author: Harley Acheson
- [View on GitHub](https://github.com/blender/blender/commit/eed83b56d2799a2a362f417aa7c2afb9eb450be4)

**9d4d1aea98e1b30278a337f440f9962afe35bf2a**
- Date: 2024-06-27
- Description: Sculpt: Add stroke stabilization to lasso tools
- Author: Sean Kim
- [View on GitHub](https://github.com/blender/blender/commit/9d4d1aea98e1b30278a337f440f9962afe35bf2a)

**8da3b74ee2a6a1412127297437172d700eed8953**
- Date: 2024-06-27
- Description: Cleanup: Remove Unneeded Old Icon-Related Files
- Author: Harley Acheson
- [View on GitHub](https://github.com/blender/blender/commit/8da3b74ee2a6a1412127297437172d700eed8953)

**6c2ffc526c37c32cf4fd15d2ad553b3827f739af**
- Date: 2024-06-23
- Description: GPv3: Copy layers to selected operator
- Author: Pratik Borhade
- [View on GitHub](https://github.com/blender/blender/commit/6c2ffc526c37c32cf4fd15d2ad553b3827f739af)

---

## Summary

This comprehensive list contains all commits made to the Blender repository during Week 26 of 2024 (June 23-29). The commits cover a wide range of areas including:

- **Sculpting improvements** (Hans Goudey, Sean Kim)
- **EEVEE rendering enhancements** (Clément Foucault, Miguel Pozo)
- **UI/UX improvements** (Harley Acheson, Julian Eisel, Dalai Felinto)
- **Extensions system development** (Campbell Barton)
- **Grease Pencil v3 features** (Falk David, Pratik Borhade)
- **Animation system work** (Nathan Vegdahl, Christoph Lendenfeld)
- **Vulkan backend improvements** (Jeroen Bakker)
- **Bug fixes and cleanup** (Various contributors)
- **Build system and dependencies** (Brecht Van Lommel, Ray Molenkamp)

The commits show active development across all major areas of Blender, with significant focus on the sculpting system refactoring, EEVEE improvements, and the new extensions system.

