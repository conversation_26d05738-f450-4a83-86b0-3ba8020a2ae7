<!-- filepath: /home/<USER>/git/blender/commits_2024-06-23_to_2024-06-29.md -->
# Blender Commits: 2024 Week 26 (June 23–29)

This file lists all commits made to the repository during ISO Week 26 of 2024 (June 23–29). Each entry includes:
- Commit hash
- Date
- Description
- Author
- Direct link to the commit on GitHub

## Commit List

---

4162aeee5f9
- Date: 2024-06-28
- Description: Fix: Sculpt: Incorrect undo behavior with modifiers and new brush code
- Author: <PERSON>
- [View on GitHub](https://github.com/blender/blender/commit/4162aeee5f9)

0d6148c514d
- Date: 2024-06-28
- Description: Cleanup: Fix assert test in BMesh sculpt brush code
- Author: <PERSON>
- [View on GitHub](https://github.com/blender/blender/commit/0d6148c514d)

07147957656
- Date: 2024-06-28
- Description: Sculpt: Improve base mesh area/normal sampling performance
- Author: <PERSON>
- [View on GitHub](https://github.com/blender/blender/commit/07147957656)

5643132c08c
- Date: 2024-06-28
- Description: Refactor: Sculpt: Specialize area/normal sampling loops per PBVH type
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/5643132c08c)

a28a9607152
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Richard Antalik
- [View on GitHub](https://github.com/blender/blender/commit/a28a9607152)

db5cc087141
- Date: 2024-06-28
- Description: Revert "Fix #118505: Incorrect strip image transformation"
- Author: Richard Antalik
- [View on GitHub](https://github.com/blender/blender/commit/db5cc087141)

fe54824f24d
- Date: 2024-06-28
- Description: Fix and test direction_to_fisheye_lens_polynomial
- Author: Alexander Brock
- [View on GitHub](https://github.com/blender/blender/commit/fe54824f24d)

c93767a8b41
- Date: 2024-06-28
- Description: Sculpt: Extend refactor to change dyntopo in brushes
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/c93767a8b41)

96018c8e8f0
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Sergey Sharybin
- [View on GitHub](https://github.com/blender/blender/commit/96018c8e8f0)

66733028402
- Date: 2024-06-28
- Description: EEVEE: Shadow: Split Tilemap finalize
- Author: Clément Foucault
- [View on GitHub](https://github.com/blender/blender/commit/66733028402)

3f3dfe16803
- Date: 2024-06-28
- Description: Cleanup: fix unused parameter compiler warning in release builds
- Author: Nathan Vegdahl
- [View on GitHub](https://github.com/blender/blender/commit/3f3dfe16803)

c8b736659ba
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Richard Antalik
- [View on GitHub](https://github.com/blender/blender/commit/c8b736659ba)

3d172170255
- Date: 2024-06-28
- Description: Fix #118505: Incorrect strip image transformation
- Author: Richard Antalik
- [View on GitHub](https://github.com/blender/blender/commit/3d172170255)

1a887c06d95
- Date: 2024-06-28
- Description: Fix #123543: Delete retiming keys deletes strip
- Author: Richard Antalik
- [View on GitHub](https://github.com/blender/blender/commit/1a887c06d95)

2362d909fc2
- Date: 2024-06-28
- Description: UI: Avoid redundant view item button lookup in context menu code
- Author: Julian Eisel
- [View on GitHub](https://github.com/blender/blender/commit/2362d909fc2)

c7e75090bd7
- Date: 2024-06-28
- Description: Fix #115981: Edge panning causes strips to overlap
- Author: Richard Antalik
- [View on GitHub](https://github.com/blender/blender/commit/c7e75090bd7)

851505752ff
- Date: 2024-06-28
- Description: Sculpt: Extend refactor to change multires in brushes
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/851505752ff)

cc1070de2da
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Sergey Sharybin
- [View on GitHub](https://github.com/blender/blender/commit/cc1070de2da)

c309479912e
- Date: 2024-06-28
- Description: Fix: Changing sculpt multires level erases sculpt changes
- Author: Sergey Sharybin
- [View on GitHub](https://github.com/blender/blender/commit/c309479912e)

8fe9c5ee04c
- Date: 2024-06-28
- Description: Cleanup: run clang format
- Author: Nathan Vegdahl
- [View on GitHub](https://github.com/blender/blender/commit/8fe9c5ee04c)

4ebcd7e1d1b
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Omar Emara
- [View on GitHub](https://github.com/blender/blender/commit/4ebcd7e1d1b)

2c345cfe31a
- Date: 2024-06-28
- Description: Fix: Compositor frees cached resources when canceled
- Author: Omar Emara
- [View on GitHub](https://github.com/blender/blender/commit/2c345cfe31a)

2eb7c4c3299
- Date: 2024-06-28
- Description: Fix: Linked images don't work in GPU compositor
- Author: Omar Emara
- [View on GitHub](https://github.com/blender/blender/commit/2eb7c4c3299)

f187e84fe37
- Date: 2024-06-28
- Description: Cleanup: Sculpt: Remove unused variables from PBVH draw data
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/f187e84fe37)

924942fadda
- Date: 2024-06-28
- Description: Cleanup: Resolve warnings after previous color painting commit
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/924942fadda)

7f2d0f7b146
- Date: 2024-06-28
- Description: Merge branch 'blender-v4.2-release'
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/7f2d0f7b146)

b62afb8cd4f
- Date: 2024-06-28
- Description: Unbreak build in sculpt_paint_color.cc
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/b62afb8cd4f)

a90a7cd521b
- Date: 2024-06-28
- Description: Refactor: Sculpt: Consolidate color painting API
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/a90a7cd521b)

acfa60c1ae0
- Date: 2024-06-28
- Description: Cleanup: rename misleading function name, quiet mypy warning
- Author: Campbell Barton
- [View on GitHub](https://github.com/blender/blender/commit/acfa60c1ae0)

155b7d68221
- Date: 2024-06-28
- Description: Refactor: Sculpt: Remove color attributes from PBVH and SculptSession
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/155b7d68221)

fba79e21dd7
- Date: 2024-06-27
- Description: Refactor: Sculpt: Replace abstract vertex abstraction for color painting
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/fba79e21dd7)

6c2ffc526c3
- Date: 2024-06-23
- Description: GPv3: Copy layers to selected operator
- Author: Pratik Borhade
- [View on GitHub](https://github.com/blender/blender/commit/6c2ffc526c3)

