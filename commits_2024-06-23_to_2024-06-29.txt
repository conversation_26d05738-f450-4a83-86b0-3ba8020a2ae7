4162aeee5f9 | 2024-06-28 | Fix: Sculpt: Incorrect undo behavior with modifiers and new brush code | <PERSON> | 
0d6148c514d | 2024-06-28 | Cleanup: Fix assert test in BMesh sculpt brush code | <PERSON> | 
07147957656 | 2024-06-28 | Sculpt: Improve base mesh area/normal sampling performance | <PERSON> | 
5643132c08c | 2024-06-28 | Refactor: Sculpt: Specialize area/normal sampling loops per PBVH type | <PERSON> | 
a28a9607152 | 2024-06-28 | Merge branch 'blender-v4.2-release' | <PERSON> | 
db5cc087141 | 2024-06-28 | Revert "Fix #118505: Incorrect strip image transformation" | <PERSON> | 
fe54824f24d | 2024-06-28 | Fix and test direction_to_fisheye_lens_polynomial | <PERSON> | 
c93767a8b41 | 2024-06-28 | Sculpt: Extend refactor to change dyntopo in brushes | <PERSON> | 
96018c8e8f0 | 2024-06-28 | Merge branch 'blender-v4.2-release' | <PERSON> | 
66733028402 | 2024-06-28 | EEVEE: Shadow: Split Tilemap finalize | Clément Foucault | 
3f3dfe16803 | 2024-06-28 | Cleanup: fix unused parameter compiler warning in release builds | Nathan Vegdahl | 
c8b736659ba | 2024-06-28 | Merge branch 'blender-v4.2-release' | Richard Antalik | 
3d172170255 | 2024-06-28 | Fix #118505: Incorrect strip image transformation | Richard Antalik | 
1a887c06d95 | 2024-06-28 | Fix #123543: Delete retiming keys deletes strip | Richard Antalik | 
2362d909fc2 | 2024-06-28 | UI: Avoid redundant view item button lookup in context menu code | Julian Eisel | 
c7e75090bd7 | 2024-06-28 | Fix #115981: Edge panning causes strips to overlap | Richard Antalik | 
851505752ff | 2024-06-28 | Sculpt: Extend refactor to change multires in brushes | Hans Goudey | 
cc1070de2da | 2024-06-28 | Merge branch 'blender-v4.2-release' | Sergey Sharybin | 
c309479912e | 2024-06-28 | Fix: Changing sculpt multires level erases sculpt changes | Sergey Sharybin | 
8fe9c5ee04c | 2024-06-28 | Cleanup: run clang format | Nathan Vegdahl | 
4ebcd7e1d1b | 2024-06-28 | Merge branch 'blender-v4.2-release' | Omar Emara | 
2c345cfe31a | 2024-06-28 | Fix: Compositor frees cached resources when canceled | Omar Emara | 
2eb7c4c3299 | 2024-06-28 | Fix: Linked images don't work in GPU compositor | Omar Emara | 
f187e84fe37 | 2024-06-28 | Cleanup: Sculpt: Remove unused variables from PBVH draw data | Hans Goudey | 
924942fadda | 2024-06-28 | Cleanup: Resolve warnings after previous color painting commit | Hans Goudey | 
7f2d0f7b146 | 2024-06-28 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
b62afb8cd4f | 2024-06-28 | Unbreak build in sculpt_paint_color.cc | Campbell Barton | 
a90a7cd521b | 2024-06-28 | Refactor: Sculpt: Consolidate color painting API | Hans Goudey | 
acfa60c1ae0 | 2024-06-28 | Cleanup: rename misleading function name, quiet mypy warning | Campbell Barton | 
155b7d68221 | 2024-06-28 | Refactor: Sculpt: Remove color attributes from PBVH and SculptSession | Hans Goudey | 
fba79e21dd7 | 2024-06-27 | Refactor: Sculpt: Replace abstract vertex abstraction for color painting | Hans Goudey | 
5427775fefc | 2024-06-28 | Anim: Theme entry for time visualization | Christoph Lendenfeld | 
c4a7c4e2a1d | 2024-06-13 | Anim: add `ID*` cache of users to Action Bindings | Sybren A. Stüvel | 
d342f64fa40 | 2024-06-28 | Core: add `BKE_lib_query_foreachid_process_main_get()` function | Sybren A. Stüvel | 
d3aa4ecfe61 | 2024-06-28 | Core: pass `bmain` to `BKE_library_foreach_ID_link()` wherever possible | Sybren A. Stüvel | 
74c4b41f1a4 | 2024-06-28 | Merge branch 'blender-v4.2-release' | Philipp Oeser | 
33ca4daf8f6 | 2024-06-28 | Fix #123882: Ocean Modifier not updating normals in "Displace" mode | Philipp Oeser | 
5b444f59548 | 2024-06-28 | Fix: GPv3: Use `OPTYPE_UNDO` for color tag operator | Pratik Borhade | 
dc72e5bac96 | 2024-06-28 | Merge branch 'blender-v4.2-release' | Philipp Oeser | 
296d05060d2 | 2024-06-28 | Fix #123560: Select by active material fails in multi-object-edit mode | Philipp Oeser | 
34342608749 | 2024-06-28 | Vulkan: Add support for Cycles CPU | Jeroen Bakker | 
da4746fe96e | 2024-06-28 | Vulkan: Fix copy depth images with stencil | Jeroen Bakker | 
eb35212f3df | 2024-06-28 | Cleanup: make format | Jacques Lucke | 
3410d0bf3ff | 2024-06-28 | Nodes: simplify node link drawing shader | Jacques Lucke | 
91bbb27f8c1 | 2024-06-28 | Fix: GPv3: Object line art not working | Pratik Borhade | 
43c3ab9acb1 | 2024-06-28 | Geometry Nodes: refactor multi-input handling in lazy-function graph generation | Jacques Lucke | 
dda4a50bf59 | 2024-06-28 | Geometry Nodes: add utility to access logged primitive values | Jacques Lucke | 
d367b6e4a29 | 2024-06-28 | Nodes: add non-const owner_tree access | Jacques Lucke | 
d819bed88fc | 2024-06-28 | Merge branch 'blender-v4.2-release' | Jacques Lucke | 
d6bf027f38a | 2024-06-28 | Fix: properly handle negative scale in safe conversion to loc/rot/scale | Jacques Lucke | 
7866fcd8699 | 2024-06-28 | Fix: convert math::Axis to vector | Jacques Lucke | 
b59e009acc4 | 2024-06-28 | UI: Add function to query debug name of view items | Julian Eisel | 
42e66f2912e | 2024-06-28 | Merge remote-tracking branch 'origin/blender-v4.2-release' | Dalai Felinto | 
b528aca7119 | 2024-06-28 | Merge remote-tracking branch 'origin/blender-v4.2-release' | Dalai Felinto | 
4bff6ba6558 | 2024-06-28 | Remove Rigify from extensions_map_from_legacy_addons.py | Sybren A. Stüvel | 
246b317bd4f | 2024-06-27 | Add-on: Rigify, move meta-rigs into a 'Rigify Meta-Rigs' sub-menu | Sybren A. Stüvel | 
d1e7346c630 | 2024-06-27 | Add-ons: Rigify, reformat code | Sybren A. Stüvel | 
09e431c511a | 2024-06-27 | Rigify: mark as support=OFFICIAL | Sybren A. Stüvel | 
3ad753e9f49 | 2024-06-18 | Fix: make foot roll rigs work again in Blender 4.2 and later (#4) | Nathan Vegdahl | 
8d4cc5d9988 | 2024-06-07 | Remove CloudRig as a promoted feature set (#1) | Demeter Dzadik | 
f5ac944658d | 2024-06-04 | Fix: Rigify Apply Toggle Pole to Keyframes clears out IK keyframes | Alexander Gavrilov | 
541e3aae25b | 2024-06-27 | Add-ons: Move Rigify into addons_core | Sybren A. Stüvel | 
270c4fad48c | 2024-06-28 | Fix #123876: Crash using eyedropper tool | Falk David | 
51e68fe0ed6 | 2024-06-28 | Cleanup: make format | Dalai Felinto | 
84f11da63ab | 2024-06-28 | UI: Extensions: Fix spacing between Install and Menu | Dalai Felinto | 
c83727f0bd7 | 2024-06-28 | Anim: add functions for asserting Project Baklava phase-1 invariants | Nathan Vegdahl | 
92c026c39ba | 2024-06-24 | I18n: Fix multi-context message extraction regex | Damien Picard |  (origin/pr-extensions-tidy-space)
cae1faec12e | 2024-06-28 | Refactor: bundle fcurve lookup/creation parameters in a struct | Nathan Vegdahl | 
bea0c5c9140 | 2024-06-28 | UI: Small Modifications to Some Icons | Harley Acheson | 
ac8da6c72ed | 2024-06-28 | Extensions: move junction_module to a private location | Campbell Barton | 
549d6fb573f | 2024-06-28 | Cleanup: Sculpt: Add assert on span size for helper method | Sean Kim | 
d0a3d629b92 | 2024-06-28 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
80f07e3f267 | 2024-06-28 | Cleanup: simplify the tags drawing function | Campbell Barton | 
dbe3956c76d | 2024-06-28 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
1043b273d59 | 2024-06-28 | Extensions: hide the extensions add-on unless debugging extensions | Campbell Barton | 
2fd7db06333 | 2024-06-28 | Extensions: hide the extensions add-on unless debugging extensions | Campbell Barton | 
60a5ef492c0 | 2024-06-28 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
37ae9d5fc47 | 2024-06-28 | Fix #123827: Extension cannot be uninstalled if symlinked | Campbell Barton | 
4ef2381ce3e | 2024-06-28 | Cleanup: UI: Remove Unused Icon Texture Drawing Code | Harley Acheson | 
0167dd08fc1 | 2024-06-28 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
9d10b88f376 | 2024-06-28 | Cleanup: remove unnecessary icon scanning on startup | Campbell Barton | 
d77ebc41de8 | 2024-06-28 | Cleanup: remove icon file lists & unused functions | Campbell Barton | 
1ede471ba2b | 2024-06-27 | Cleanup: Remove unnecessary namespaces, pass math types by value | Hans Goudey | 
868fed96c92 | 2024-06-27 | Cleanup: Sculpt: Remove unused sculpt clay brush code | Sean Kim | 
4d84940253f | 2024-06-27 | UI: File Browser File Type Icons Use Shader Outline | Harley Acheson | 
69672559066 | 2024-06-27 | Color management: Support white balance as part of the display transform | Lukas Stockner | 
9ae237d0b48 | 2024-06-27 | UI: Allow Discretionary Use of Icon Outline | Harley Acheson | 
5c7821fd793 | 2024-06-27 | imbuf: Add regression test for PSD | Jesse Yurkovich | 
45c03764bf7 | 2024-06-27 | imbuf: Add PSD test files | Jesse Yurkovich | 
8f64329c795 | 2024-06-27 | Refactor: Use newer attribute API  in `create_liquid_geometry` | Falk David | 
d16eac9ceee | 2024-06-27 | Merge branch 'blender-v4.2-release' | Sergey Sharybin | 
9e4d9295f0f | 2024-06-27 | Fix: Compilation error of fribidi on macOS | Sergey Sharybin | 
e1ca54c52a8 | 2024-06-27 | Fix #123768: Sculpt: Crash on undo with multires | Hans Goudey | 
39fe42fcac7 | 2024-06-27 | Windows: 4.2 Library incremental (OIIO+OIDN) | Anthony Roberts | 
a0f0a4dd49f | 2024-06-27 | Cleanup: Remove `BKE_attribute_allow_procedural_access` | Falk David | 
baf07c22bdb | 2024-06-27 | Revert "Fix #123794: Crash when UDIMs have gray and color tiles" | Miguel Pozo | 
095e78bd28b | 2024-06-27 | Fix #123794: Crash when UDIMs have gray and color tiles | Miguel Pozo | 
b2f8f5a491f | 2024-06-27 | Fix: USD import: domelight Y-up orientation | Michael Kowalski | 
e75dfaf17b2 | 2024-06-27 | Merge branch 'blender-v4.2-release' | Sergey Sharybin | 
32588169d3f | 2024-06-27 | Fix: Initialization of paint mode fails in certain files | Sergey Sharybin | 
a8fae77f103 | 2024-06-27 | Cleanup: remove unused icon utilities and make convenience target | Campbell Barton | 
9584f8fb550 | 2024-06-27 | Cleanup: Sculpt: Fix misleading naming of grids index buffer functions | Hans Goudey | 
dc70f454b7e | 2024-06-27 | Cleanup: Sculpt: Extract vertex buffer filling into separate functions | Hans Goudey | 
1453136ad06 | 2024-06-27 | Cleanup: Sculpt: Simplify drawing visible face counting | Hans Goudey | 
98939c29d4e | 2024-06-27 | Merge branch 'blender-v4.2-release' | Christoph Lendenfeld | 
f3b393a74ac | 2024-06-27 | Fix #123738: Keyframe drawing issue while duplicating keys | Christoph Lendenfeld | 
e72e538fdd0 | 2024-06-27 | Vulkan: Fix sequential read hazard | Jeroen Bakker | 
15ef3725678 | 2024-06-27 | Merge branch 'blender-v4.2-release' | Hans Goudey | 
8945b7e49af | 2024-06-27 | Fix #123809: Sculpt visibility invert missing PBVH node update | Hans Goudey | 
ef8f14f3d61 | 2024-06-27 | Fix #90923: Bone Stick active color | Christoph Lendenfeld | 
adfe6880464 | 2024-06-27 | Fix another batch of mismatches `MEM_new`/`MEM_freeN` cases in UI/Assets code. | Bastien Montagne | 
643334b7278 | 2024-06-26 | Sculpt: Resolve over-allocation of multires draw vertex buffers | Hans Goudey | 
83a15be1099 | 2024-06-26 | Sculpt: Improve multires drawing performance by simplifying logic | Hans Goudey | 
7cf9854938d | 2024-06-26 | Sculpt: Avoid vertex buffer access overhead for multires drawing | Hans Goudey | 
b013eed9672 | 2024-06-26 | Sculpt: Remove double function call indirection in multires drawing | Hans Goudey | 
3009c98d5f1 | 2024-06-27 | Vulkan: Fix read-after-write hazard in draw manager visibility | Jeroen Bakker | 
fb2e712991d | 2024-06-27 | VSE: Clarify wording for Replace Selection property | Richard Antalik | 
cb68d0c9d73 | 2024-06-27 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
1553a75fedd | 2024-06-27 | Fix regression installing an addon by drag'n'drop | Andrej730 | 
84e5de33278 | 2024-06-27 | Fix: GPv3: Crash in `grease_pencil_object_cache_populate` | Falk David | 
8e2cbdc876c | 2024-06-27 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
c3d18854f38 | 2024-06-27 | Cleanup: avoid unnecessary separator | Campbell Barton | 
6f06e2258f9 | 2024-06-27 | Image: Clarify color depth tooltip for EXR images | Omar Emara | 
f1bfaaf2f78 | 2024-06-27 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
fb94028d10c | 2024-06-27 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
e9cba0e5887 | 2024-06-27 | Fix #123670: EEVEE: Add support for new grease pencil type | Jeroen Bakker | 
42e1239ba89 | 2024-06-27 | Core: support restricting the types an XML preset may load | Campbell Barton | 
65d0f365a96 | 2024-06-27 | Cleanup: correct misleading name of internal function | Campbell Barton | 
cb9060ede69 | 2024-06-27 | Merge branch 'blender-v4.2-release' | Sergey Sharybin | 
2dc4bd3cdf5 | 2024-06-27 | Fix: PSD images are read wrong | Sergey Sharybin | 
6aa6aee2d5d | 2024-06-27 | Reapply "Fix (unreported) Assets: MEM_new/MEM_freeN mismatch usages." | Bastien Montagne | 
c6e452d865a | 2024-06-27 | UI: Extensions: Add a separator between Install and the "⌄" button | Dalai Felinto | 
6320066a880 | 2024-06-27 | Merge branch 'blender-v4.2-release' | Brecht Van Lommel | 
b802f146e60 | 2024-06-27 | Tools: Move warning about wrong clang-format version to the bottom | Brecht Van Lommel | 
cb76781be74 | 2024-06-27 | Revert "Fix (unreported) Assets: MEM_new/MEM_freeN mismatch usages." | Bastien Montagne | 
77f874ae1dd | 2024-06-27 | Fix #123782: Asset Browser does not show tags for active asset | Philipp Oeser | 
da05bff96c2 | 2024-06-27 | Fix (unreported) Assets: MEM_new/MEM_freeN mismatch usages. | Bastien Montagne | 
f43cf396891 | 2024-06-27 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
989de85cf63 | 2024-06-27 | Extensions: fixed & refactor internals for extension visibility | Campbell Barton | 
99c75beabfc | 2024-06-27 | Cleanup: Silence unused variable warning | Sean Kim | 
11d0a9db1b1 | 2024-06-27 | Fix: Build error from missing change in recent commit | Sean Kim | 
1a392104c3b | 2024-06-27 | Cleanup: Sculpt: Add BLI_NOINLINE to mask.cc helper methods | Sean Kim | 
0bc7631693f | 2024-06-27 | Sculpt: Initial data-oriented refactor for clay brush | Sean Kim | 
6b7255b978b | 2024-06-27 | Refactor: Sculpt: Use return value instead of reference for orig_data | Sean Kim | 
eed83b56d27 | 2024-06-27 | UI: Calm Warnings With New Icons | Harley Acheson | 
9d4d1aea98e | 2024-06-27 | Sculpt: Add stroke stabilization to lasso tools | Sean Kim | 
8da3b74ee2a | 2024-06-27 | Cleanup: Remove Unneeded Old Icon-Related Files | Harley Acheson | 
4d29e1dccb1 | 2024-06-26 | Refactor: Sculpt: Remove some constants from PBVH | Hans Goudey | 
c365daaedb0 | 2024-06-26 | Cleanup: Sculpt: Remove another unused PBVH variable | Hans Goudey | 
d3d9be486be | 2024-06-26 | Cleanup: Sculpt: Remove unused PBVH draw cache status variables | Hans Goudey | 
b7256b1ea7f | 2024-06-26 | UI: Use SVG Icon for Logo in Splash About | Harley Acheson | 
3072e6f5188 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Brecht Van Lommel | 
4e4d8476c49 | 2024-06-26 | Tools: Remove cycles commit syncs script, moved to the standalone repo | Brecht Van Lommel | 
4b47a48ea29 | 2024-06-26 | Hydra: Fix Cycles render delegate to build with USD 24.x | Brecht Van Lommel | 
5c377686e75 | 2024-06-26 | UI: Use SVG Icons for Alert Icons on Dialogs | Harley Acheson | 
a060de65a4d | 2024-06-26 | Cycles: Sync minor build related changes with standalone repo | Brecht Van Lommel | 
5e40dcc95d1 | 2024-01-15 | Cycles: Expose object node in XML API | howetuft | 
dc358521072 | 2024-02-09 | Cycles: Expose vertex normals and tangent space attributes in XML API | howetuft | 
3be050ed47a | 2023-06-22 | Fix: Cycles build error with GCC and Clang with some build options | Pierre Pontier | 
d9bd35c4bc6 | 2024-06-26 | Cleanup: make format | Brecht Van Lommel | 
fe4c7c41782 | 2024-06-26 | UI: Consistent Status Bar Spacing | Harley Acheson | 
aa4c42958da | 2024-06-26 | Fix: Sculpt: Anchored brush "restore" broken for refactored brushes | Hans Goudey | 
992e47bc456 | 2024-06-26 | Fix #123675: Use ICON_TYPE_MONO_TEXTURE Icons for Error Conditions | Harley Acheson | 
f6358f6e71a | 2024-06-26 | Merge branch 'blender-v4.2-release' | Bastien Montagne | 
f739d4832e3 | 2024-06-15 | I18n: Extract and disambiguate a few messages | Damien Picard | 
26cdf7e3401 | 2024-06-15 | I18n: Extract many custom labels defined in uiItem* | Damien Picard | 
a50ab48709e | 2024-06-26 | Core: Cleanup: foreach_id: document acceptable return values from callbacks. | Bastien Montagne | 
36ff8c02315 | 2024-06-26 | Fix: EEVEE: Shadow visibility flag not versioned for 4.1 files | Clément Foucault | 
1d7dc7190fc | 2024-06-26 | Sculpt: Refactor distance falloff, clipping, and brush strength | Hans Goudey | 
bbfd3d18966 | 2024-06-26 | Fix (unreported) Subdiv: `MEM_freeN` called on `MEM_new`-created data. | Bastien Montagne | 
c8340cf7541 | 2024-06-26 | Cycles: Remove AMD and Intel GPU support from Metal backend | Alaska | 
d4de4c1f012 | 2024-06-26 | Fix: EEVEE: Shadow: Jitter more than the light radius | Clément Foucault | 
eb37bace969 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Miguel Pozo | 
2804b000ac6 | 2024-06-26 | Fix: GPU: Fix handling of invalid binary caches | Miguel Pozo | 
bbf4d136838 | 2024-06-26 | Cleanup: Formatting | Hans Goudey | 
e8f5cc2dc2f | 2024-06-27 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
99fcbf877de | 2024-06-26 | Extensions: Remove "Enabled" section, use sorting instead | Campbell Barton | 
de14cb1c1e0 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Philipp Oeser | 
d4f5d4a6f60 | 2024-06-26 | Fix #123770: Vertex paint Accumulate option breaks Blur action | Philipp Oeser | 
68d998f4ca0 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Philipp Oeser | 
bc0b86797cc | 2024-06-26 | Fix #94125: Collada: not all edit mode changes are exported | Philipp Oeser | 
3851157405f | 2024-06-26 | Merge branch 'blender-v4.2-release' | Philipp Oeser | 
56023d5b63a | 2024-06-26 | Fix: EEVEE: Transparent pass break AO pass | Clément Foucault | 
581a01a92d7 | 2024-06-26 | Fix: GPv3: "Chisel Marker" brush size pressure default | Falk David | 
d1a01ceff21 | 2024-06-26 | Fix: GPv3: Draw Tool: Angle setting alignement | Falk David | 
3a0236c8893 | 2024-06-26 | Curves: Return empty span from `offsets()` if there are no curves | Falk David | 
02a45fb60d0 | 2024-06-26 | Vulkan: Add read dependency for push constants fallback | Jeroen Bakker | 
af71cb64857 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Sergey Sharybin | 
32324581525 | 2024-06-26 | Fix #123763: Cycles Metal renders with MNEE stuck on some Macs | Alaska | 
09ab585a05d | 2024-06-26 | Merge branch 'blender-v4.2-release' | Jacques Lucke | 
b120440f7cf | 2024-06-26 | Fix: Nodes: show group icon for custom node groups | Miguel Porces | 
89471e8566f | 2024-06-26 | Merge branch 'blender-v4.2-release' | Jacques Lucke | 
29f6167db1f | 2024-06-26 | Fix #123624: no geometry after applying visual geometry to mesh | Jacques Lucke | 
2c773ce857d | 2024-06-26 | Fix: GPv3: Draw Tool: First point too large when using angle factor | Falk David | 
e9291548d33 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Pratik Borhade | 
1dd017ac624 | 2024-06-26 | Fix #123228: GPU subdivision setting can be adjusted on an unsupported device | Pratik Borhade | 
5dbb769c083 | 2024-06-26 | Fix #123617: Scrollbar barely visible in light theme | Pratik Borhade | 
f1f04d0e87a | 2024-06-26 | Merge branch 'blender-v4.2-release' | Bastien Montagne | 
2afcc9246f0 | 2024-06-26 | Fix (unreported) CMake choosing system wayland paths over pre-built ones. | Bastien Montagne | 
2d7f00e4107 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Bastien Montagne | 
c525e4db836 | 2024-06-26 | Revert "GHOST/Wayland: reference the stable tablet-v2 API" | Bastien Montagne | 
c3b5a889bdc | 2024-06-26 | Cleanup: GPv3: Draw Tool: Remove unnecessary lambdas | Falk David | 
db25962c276 | 2024-06-26 | Cleanup: GPv3: Draw Tool: Use `math::numbers::pi` | Falk David | 
8748225d589 | 2024-06-26 | GPv3: Draw Tool: Random Color | Falk David | 
507169ca69e | 2024-06-26 | Merge branch 'blender-v4.2-release' | Bastien Montagne | 
73db6314407 | 2024-06-25 | Fix broken tests build in multi-binaries case. | Bastien Montagne | 
da814712a5b | 2024-06-25 | Fix broken tests build in multi-binaries case. | Bastien Montagne | 
4a37e8ed7b9 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Sergey Sharybin | 
0081c4b64a6 | 2024-06-26 | Fix #123576: VSE Crash: Preview render fails if two Scene strips are stacked | Sergey Sharybin | 
be3942e5de0 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Lukas Tönne | 
864d7010836 | 2024-06-26 | Fix #123705: Object Info node is not setting Transform output | Lukas Tönne | 
58f19f41cec | 2024-06-26 | Fix: EEVEE displays wrong pass if Cryptomatte is enabled | Omar Emara | 
9ee6da08ab1 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
2a85eaaf169 | 2024-06-26 | GHOST/Wayland: reference the stable tablet-v2 API | Campbell Barton | 
9bff63e4169 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
ce9500dc507 | 2024-06-26 | Linux: update wayland deps | Campbell Barton | 
4671629f4f4 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
bb4946135a5 | 2024-06-26 | Deps: bump version of wayland libraries | Campbell Barton | 
c89b93ca4ed | 2024-06-26 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
2d8d3bd0f8e | 2024-06-26 | Linux: update OpenImageDenoise | Campbell Barton | 
4f588552227 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
1291ac6590a | 2024-06-26 | Linux: update OpenImageIO | Campbell Barton | 
66e422f33dd | 2024-06-26 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
8b3ec865618 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
96906536dbc | 2024-06-26 | Extensions: add a Python API for user editable extension directories | Campbell Barton | 
f5aaee39d21 | 2024-06-26 | Extensions: prevent recursive file removal for corrupt repositories | Campbell Barton | 
d16aa62529f | 2024-06-26 | Merge branch 'blender-v4.2-release' | Richard Antalik | 
44bc433ed40 | 2024-06-26 | VSE: Copy channels when making meta strip | Richard Antalik | 
20751744962 | 2024-06-26 | Fix #120370: Fades don't work well with scene strips | Richard Antalik | 
b933d6e1c7c | 2024-06-26 | Fix #123586: Sequencer: Box select in Preview doesn't work after selecting it in the menu | Nika Kutsniashvili | 
3be54248961 | 2024-06-25 | UI: Remove blender_icons.svg and icon DAT files | Harley Acheson | 
1d47e3276b2 | 2024-06-26 | UI: File Browser Thumbnail View SVG Icons | Harley Acheson | 
5998bab73f5 | 2024-06-25 | Cleanup: Sculpt: Remove unused PBVH is_drawing variable | Hans Goudey | 
9b9ed0a76de | 2024-06-25 | Refactor: Sculpt: Remove corner_tris reference from PBVH | Hans Goudey | 
59d6eae1160 | 2024-06-26 | Sculpt: Reuse existing mesh triangles cache in sculpt mode | Hans Goudey | 
31de58e161f | 2024-06-26 | Merge branch 'blender-v4.2-release' | Brecht Van Lommel | 
224307b9aa4 | 2024-06-26 | Fix: Cycles build error with OptiX after recent changes | Brecht Van Lommel | 
06ccf0c3385 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
906dfdfb671 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
4cf4f6d06ed | 2024-06-26 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
fcad7e7d2bc | 2024-06-25 | Fix mathutils array parsing exception message | Andrej730 | 
30ad15c01cf | 2024-06-26 | Fix: #123349 Incorrect initialization of face sets by material | Mangal Kushwah | 
7dfedf4e847 | 2024-06-26 | Tools: various fixes/corrections to the autopep8 formatter | Campbell Barton | 
983b0b61826 | 2024-06-26 | UI: Converting SVG Icons to Bitmaps | Harley Acheson | 
840457c45c8 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Miguel Pozo | 
066619e1a8c | 2024-06-25 | Fix: GPU: GLStorageBuf::async_flush_to_host non named path | Miguel Pozo | 
c5e7498f89f | 2024-06-25 | GPv3: Draw Tool: Random Rotation | Falk David | 
e49fda3ff84 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Weizhen Huang | 
02e6985c628 | 2024-06-25 | Fix #94323: Cycles blocky artifacts in overlapping volumes due to scale difference | Weizhen Huang | 
42e015e77c0 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Miguel Pozo | 
9b61673b463 | 2024-06-25 | Fix: EEVEE:  Add missing view bind | Miguel Pozo | 
fa2c7ccdda5 | 2024-06-25 | Fix (unreported) memleak in some BKE tests in multi-binaries case. | Bastien Montagne | 
b53ae4745c7 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Bastien Montagne | 
fccdfa2b06b | 2024-06-25 | Fix (unreported) missing IDTypes init in lib_remapper tests. | Bastien Montagne | 
54456d27c95 | 2024-06-26 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
6456210d30a | 2024-06-26 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
a8d6047e6ee | 2024-06-26 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
fcba8b8099c | 2024-06-26 | Fix #123710: Remote repo re-used when installing from file selector | Campbell Barton | 
17951d12ed2 | 2024-06-26 | Cleanup: correct staticmethod, remove unused imports & formatting | Campbell Barton | 
69b22f73c2c | 2024-06-26 | Revert "UI: Extensions: Remove "Enabled" section, use sorting instead" | Campbell Barton | 
17ba53f6057 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Miguel Pozo | 
b8587c96ec7 | 2024-06-25 | Fix #120628: Transfer mode overlay flickering artifacts | Miguel Pozo | 
5bf5322c3f1 | 2024-06-25 | Merge remote-tracking branch 'origin/blender-v4.2-release' | Dalai Felinto | 
36060eda49f | 2024-06-25 | UI: Extensions: Show the correct URL icon for extension links | Dalai Felinto | 
90dc001a56e | 2024-06-25 | UI: Extensions: Remove "Enabled" section, use sorting instead | Dalai Felinto | 
451212a4fb4 | 2024-06-25 | Fix: Error when building without experimental features | Falk David | 
b855cd7bb0b | 2024-06-25 | Merge branch 'blender-v4.2-release' | Miguel Pozo | 
f16cf49c8ac | 2024-06-25 | Cleanup: EEVEE: Clarify Jitter Camera tooltip | Miguel Pozo | 
b1dfdca3926 | 2024-06-25 | UI: Extensions: Fix Extension tagline not disabled when it should | Dalai Felinto | 
f1134ce0b7d | 2024-06-25 | Merge branch 'blender-v4.2-release' | Omar Emara | 
f19a9e9b4d4 | 2024-06-25 | Fix #123607: Plane Track Deform produces wrong output | Omar Emara | 
ee0b7b9a954 | 2024-06-25 | Vulken: Mix array aspect of image views | Jeroen Bakker | 
583ad3460a1 | 2024-06-25 | UI: Extensions: Changes on the Preferences Tabs | Dalai Felinto | 
8ee43f34be5 | 2024-06-25 | UI: Simplify tooltip for the preferences active section | Dalai Felinto | 
dd7cc117969 | 2024-06-25 | Extensions: Link to visit the extensions platform | Dalai Felinto | 
92a2185a4e7 | 2024-06-25 | UI: Extensions: Use placeholder for extensions and add-ons search | Dalai Felinto | 
33a7cf8c494 | 2024-06-25 | Translations: blender.org should not be translatable | Dalai Felinto | 
4fd39f4103a | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
b5e0bff4ba2 | 2024-06-25 | Extensions: show the "Website" in the expanded details | Campbell Barton | 
b1d655d4f39 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
2783790bf2a | 2024-06-25 | Extensions: use the remote repositories URL for the add-ons "Website" | Campbell Barton | 
56566515d4c | 2024-06-25 | GPv3: Update default brush settings | Falk David | 
e067b11c3ce | 2024-06-25 | Anim: add non-const version of `animrig::fcurves_all(action)` | Sybren A. Stüvel | 
111d4e5837c | 2024-06-25 | Fix: Misleading description of the samples count pass | Sergey Sharybin | 
fb0f4092375 | 2024-06-25 | Fix #123716: autokeying Shape Key fields does not work | Nathan Vegdahl | 
38f889d07a0 | 2024-06-25 | Fix #123538: Crossfade sounds sets volume to 0 | Nathan Vegdahl | 
2034884a812 | 2024-06-25 | Anim: make auto-keying in shapekey anim editor work for layered actions | Nathan Vegdahl | 
220a66e54d5 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
7298df864cc | 2024-06-25 | Fix error detecting outdated extensions in the UI | Campbell Barton | 
5685ec59528 | 2024-06-25 | GPv3: Draw Tool: Use brush strength for fill opacity | Falk David | 
b2a049e77f9 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
4915e6dc83e | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
696c997d72c | 2024-06-25 | Extensions: split extensions by category Enabled/Installed/Available | Campbell Barton | 
56fdfe57931 | 2024-06-25 | Fix typo in VSE / Movieclip strip type. | Thomas Dinges | 
d29d410a2f7 | 2024-06-25 | GPv3: Draw Tool: Show "Cursor" menu | Falk David | 
88387e3a821 | 2024-06-25 | GPv3: Draw Tool: Randomize radius and opacity | Falk David | 
fa39948602b | 2024-06-25 | Fix #123583: Facesets not visible in EEVEE | Jeroen Bakker | 
f4c1f42cd25 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
6a4a56f0670 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
2da9e895238 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
b038442b326 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
b10d7f12906 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
a142a7a5386 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
00d3e19296f | 2024-06-25 | Extensions: defer imports to reduce overhead on startup | Campbell Barton | 
78f37fe331c | 2024-06-25 | Vulkan: Fix incorrect image aspect | Jeroen Bakker | 
f5b173188e2 | 2024-06-25 | Vulkan: Fix incorrect read image barrier | Jeroen Bakker | 
50bda25f101 | 2024-06-25 | Cleanup: make format | Jeroen Bakker | 
fab15384a4b | 2024-06-18 | I18n: Node Wrangler: Use proper translation contexts in the UI | Damien Picard | 
dba5a393ca1 | 2024-06-18 | I18n: Node Wrangler: translate operator reports using rpt_() | Damien Picard | 
db6d05537c3 | 2024-06-18 | UI: Node Wrangler: Fix message case and grammar | Damien Picard | 
34dcfb365b7 | 2024-06-25 | Fix #88208: Multi user action missing depsgraph update | Christoph Lendenfeld | 
47b2e9a8575 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
d4bdc4f7a2f | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
0c580d075bb | 2024-06-25 | Fix #123676: error printed when installing extensions from disk | Campbell Barton | 
5b5e8693070 | 2024-06-25 | Extensions: skip reading remote meta-data when accessing local meta-data | Campbell Barton | 
4e2cff5df73 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
02b2f75da7d | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
6fe666f7738 | 2024-06-25 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
6b6341f9a7c | 2024-06-25 | Extensions: suppress repository access errors while synchronizing | Campbell Barton | 
3834e296a3e | 2024-06-25 | Extensions: de-duplicate redundant synchronization requests | Campbell Barton | 
4712aca2a84 | 2024-06-25 | Fix #123682: extension repo URL/access token changes don't re-sync | Campbell Barton | 
d8637857759 | 2024-06-24 | Merge branch 'blender-v4.2-release' | Sean Kim | 
2cc6de8f7ee | 2024-06-24 | Fix #123574: Automask area normal causes garbled meshes | Sean Kim | 
4919b5681de | 2024-06-24 | Merge branch 'blender-v4.2-release' | Hans Goudey | 
1b80125a67f | 2024-06-24 | Fix: Leak of initialized but not pushed undo step | Hans Goudey | 
59d84a4c422 | 2024-06-24 | Fix: Sculpt: Various issues with undo | Hans Goudey | 
a5fc69ede54 | 2024-06-24 | Merge remote-tracking branch 'origin/blender-v4.2-release' | Ray Molenkamp | 
68b85d1e682 | 2024-06-24 | Windows_X64: OIIO rebuild with upstream PR 4302 | Ray Molenkamp | 
b7277f5f96c | 2024-06-24 | Merge remote-tracking branch 'origin/blender-v4.2-release' | Ray Molenkamp | 
8082d922732 | 2024-06-24 | deps: oiio include upstream PR 4302 | Ray Molenkamp | 
0f63085d9df | 2024-06-24 | Merge branch 'blender-v4.2-release' | Brecht Van Lommel | 
1ea70c3e3e9 | 2024-06-24 | Fix: macOS x265 libraries have wrong deployment target | Brecht Van Lommel | 
0b7ae0c75a2 | 2024-06-24 | EEVEE: Reduce binary size on Mesa caused by fixed size arrays | Clément Foucault | 
65c5721c5a4 | 2024-06-24 | Fix (unreported): MEM_new/MEM_freeN mistaches. | Bastien Montagne | 
62293d76bbc | 2024-06-24 | Merge branch 'blender-v4.2-release' | Miguel Pozo | 
e140f263d08 | 2024-06-24 | Fix #122856: Sculpt trim and filter tools do not show brush cursor | Sean Kim | 
d88150ce1f6 | 2024-06-24 | Fix: GPU: Skip binaries larger than the shared memory | Miguel Pozo | 
65de0db503e | 2024-06-24 | I18n: translate mouse and NDOF events in the keymap preferences | Damien Picard | 
df76e76cda2 | 2024-06-24 | Windows: OIDN 2.3.0 | Ray Molenkamp | 
65e5db1f958 | 2024-06-24 | Windows_x64: OIDN 2.3.0 | Ray Molenkamp | 
9ffb277e014 | 2024-06-24 | GPv3: Draw Tool: Jitter option | Falk David | 
3e28b6c7f11 | 2024-06-24 | Nodes: File path socket subtype for string sockets | Devashish Lal | 
4bb5cd365b2 | 2024-06-24 | Curves: Remove unnecessary `to_static_type` in resample code | Lukas Tönne | 
e14d153edfb | 2024-06-24 | Cleanup: Formatting | Hans Goudey | 
8c430a7a678 | 2024-06-24 | Depsgraph: Optimize composite and render pipeline depsgraph | Sergey Sharybin | 
55ac7fd6fd5 | 2024-06-24 | Vulkan: Early exit clearing stencils | Jeroen Bakker | 
30ec6ac9618 | 2024-06-24 | EEVEE: Missing resource when tracing planar probes | Jeroen Bakker | 
bba375c7855 | 2024-06-24 | GPv3: Sculpt Advanced panel is displayed empty | Pratik Borhade | 
371e419aa47 | 2024-06-24 | Fix: #122183: GPv3: Primitive vertex color not working on fills | casey bianco-davis | 
8c647e71082 | 2024-06-24 | Fix #123396: GPencil: Show brush options for GPv3 | YimingWu | 
db9ce7392da | 2024-06-24 | GPv3: Use texture coordinates for primitive tools. | casey bianco-davis | 
830fb50c0a4 | 2024-06-24 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
7a0f205b898 | 2024-06-24 | Fix #123657: setting the theme manually breaks the Extension UI | Campbell Barton | 
9688462349d | 2024-06-24 | Merge branch 'blender-v4.2-release' | Bastien Montagne | 
a94798d31c3 | 2024-06-24 | Fix (unreported) assert in recursive 'foreach id' iterating code. | Bastien Montagne | 
a196f4276ed | 2024-06-24 | Merge branch 'blender-v4.2-release' | Philipp Oeser | 
696848204c9 | 2024-06-24 | Fix #118148: STL/PLY: Imported object data has increased usercount | Philipp Oeser | 
a09edde30da | 2024-06-24 | Merge branch 'blender-v4.2-release' | Philipp Oeser | 
f4afd404e56 | 2024-06-24 | Merge branch 'blender-v4.2-release' | Weizhen Huang | 
e4cfa1b8ba8 | 2024-06-24 | EEVEE: Subsurface missing bindings | Jeroen Bakker | 
f51aa8a2c7c | 2024-06-24 | Fix: GPv3: Eraser and Tint tool use wrong radius | Falk David | 
6fbc958e895 | 2024-06-24 | Fix: Cycles Light Tree gives low weight to distant lights in large volume | Weizhen Huang | 
3eb15247fe0 | 2024-06-24 | Merge branch 'blender-v4.2-release' | Jeroen Bakker | 
1663df0c8fc | 2024-06-24 | EEVEE: Ray generation missing resource | Jeroen Bakker | 
34a679d19f4 | 2024-06-24 | Vulkan: Read out of bound when using many resources | Jeroen Bakker | 
8d8a57332a4 | 2024-06-24 | Merge branch 'blender-v4.2-release' | Jacques Lucke | 
c1c3ed0e4b8 | 2024-06-24 | Fix #123542: memory display error | Jacques Lucke | 
069e32b2bfc | 2024-06-24 | Merge branch 'blender-v4.2-release' | Bastien Montagne | 
236adb9449e | 2024-06-24 | I18N: Updated UI translations from git/weblate repo (5a0da5c5c520b306). | Bastien Montagne | 
33b81476785 | 2024-06-24 | Build: Update macOS libraries for Blender 4.2 | Raul Fernandez Hernandez | 
9e267bbd577 | 2024-06-24 | Cycles: Use denoising device info to pick automatic denoiser | Alaska | 
a606f2fd09d | 2024-06-24 | Merge branch 'blender-v4.2-release' | Lukas Tönne | 
37e2a27bddd | 2024-06-24 | Fix #80876: Ocean modifier "Delete Bake" button does not work | Lukas Tönne | 
560b36ac8db | 2024-06-24 | EEVEE: Fix incorrect texture usage DoF | Jeroen Bakker | 
0c39a756345 | 2024-06-24 | Vulkan: Fix binding collision when mixing textures and images | Jeroen Bakker | 
9cb45ba6c33 | 2024-06-24 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
99a871d2f3a | 2024-06-24 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
4e734dc66de | 2024-06-24 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
213a968e052 | 2024-06-24 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
0b14da7391b | 2024-06-24 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
24be61b3d74 | 2024-06-24 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
36cf7625a0a | 2024-06-24 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
1917680622d | 2024-06-24 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
d8fafad7040 | 2024-06-24 | Extensions: clarify installing multiple files is supported | Campbell Barton | 
2eaf70a4ed6 | 2024-06-24 | Extensions: adjust the error about failure to sync when offline | Campbell Barton | 
e48a9fc5ed1 | 2024-06-24 | Extensions: add missing update callbacks for repository flags | Campbell Barton | 
9b7f2c9976c | 2024-06-24 | Extensions: don't reuse the last remote repository when dropping files | Campbell Barton | 
25fc66f4724 | 2024-06-24 | Extensions: use case insensitive sort for the repositories enum | Campbell Barton | 
ef1e538693b | 2024-06-24 | Extensions: prioritize the remote website over the local data | Campbell Barton | 
efa1f5847e9 | 2024-06-24 | Extensions: don't attempt to sync after adding a repo in background mode | Campbell Barton | 
3ef073ed1dc | 2024-06-24 | Cleanup: use variable to access extensions private directory | Campbell Barton | 
b59fbbe8654 | 2024-06-24 | Cleanup: Formatting | Lukas Stockner | 
26eb5d9899c | 2024-06-24 | EEVEE: Adapt Principled BSDF to closure sampling | Lukas Stockner | 
3b9081d093a | 2024-06-23 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
b1287ad6d46 | 2024-06-23 | Cleanup: remove unused extensions.repo_add_from_drop operator | Campbell Barton | 
4d9398318c7 | 2024-06-23 | Merge branch 'blender-v4.2-release' | Campbell Barton | 
7298ed6c83b | 2024-06-23 | Extensions: don't lock the UI when syncing before handling a dropped URL | Campbell Barton | 
6c2ffc526c3 | 2024-06-23 | GPv3: Copy layers to selected operator | Pratik Borhade | 