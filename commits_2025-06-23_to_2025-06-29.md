# Blender Commits: 2025 Week 26 (June 23–29)

This file contains ALL commits made to the Blender repository during ISO Week 26 of 2025 (June 23–29). Each entry includes:
- Full commit hash
- Date
- Description
- Author
- Direct link to the commit on GitHub

**Total commits found: 200+**

## Commit List (Chronological Order - Earliest First)

---

**c8aa20f8b370d44bdb3bd37fc67b93e3545a0441**
- Date: 2025-06-22
- Description: Cleanup: Remove titlebar text color from decoration style settings
- Author: <PERSON>
- [View on GitHub](https://github.com/blender/blender/commit/c8aa20f8b370d44bdb3bd37fc67b93e3545a0441)

**6b8970d7286861240d6c353eb389c3585a465977**
- Date: 2025-06-23
- Description: Fix: VSE: Using redo panel causes operator to fail
- Author: <PERSON>
- [View on GitHub](https://github.com/blender/blender/commit/6b8970d7286861240d6c353eb389c3585a465977)

**e5ddffd5d1c21c15b99d8cc768167524cdbe0f13**
- Date: 2025-06-23
- Description: Merge branch 'blender-v4.5-release'
- Author: Richard Antalik
- [View on GitHub](https://github.com/blender/blender/commit/e5ddffd5d1c21c15b99d8cc768167524cdbe0f13)

**cc5ba711147fa51dab2382a59bb0be6cde7b2599**
- Date: 2025-06-23
- Description: Refactor: UI: Add uiLayout fixed_size, red_alert, root_panel, search_weight and width methods
- Author: Guillermo Venegas
- [View on GitHub](https://github.com/blender/blender/commit/cc5ba711147fa51dab2382a59bb0be6cde7b2599)

********************************************
- Date: 2025-06-23
- Description: Fix #139882: Vulkan/USD: Crash on AMD GPUs
- Author: Jeroen Bakker
- [View on GitHub](https://github.com/blender/blender/commit/****************************************)

**41e93e3d9b89045dadc9de854f667cdeabda9409**
- Date: 2025-06-23
- Description: Merge branch 'blender-v4.5-release'
- Author: Jeroen Bakker
- [View on GitHub](https://github.com/blender/blender/commit/41e93e3d9b89045dadc9de854f667cdeabda9409)

**ea618c20273fb2d3824d57033064e012ca59c608**
- Date: 2025-06-23
- Description: Refactor: Nodes: improve socket value variant construction
- Author: Jacques Lucke
- [View on GitHub](https://github.com/blender/blender/commit/ea618c20273fb2d3824d57033064e012ca59c608)

**26cda0e1e9c0f3db78a4ee370699565da97fa283**
- Date: 2025-06-23
- Description: I18N: Updated UI translations from git/weblate repository (8176c1461).
- Author: Bastien Montagne
- [View on GitHub](https://github.com/blender/blender/commit/26cda0e1e9c0f3db78a4ee370699565da97fa283)

**7082142fb569bd8f96177593c932d1fc907bd704**
- Date: 2025-06-23
- Description: Merge branch 'blender-v4.5-release'
- Author: Bastien Montagne
- [View on GitHub](https://github.com/blender/blender/commit/7082142fb569bd8f96177593c932d1fc907bd704)

**a5399af3883fe83a6732d809a1cfc923a64f0b11**
- Date: 2025-06-23
- Description: Fix: EEVEE: Wrong versionning
- Author: Clément Foucault
- [View on GitHub](https://github.com/blender/blender/commit/a5399af3883fe83a6732d809a1cfc923a64f0b11)

**f0c7e52ff283f7b0832913b61c3e4b72bb5de764**
- Date: 2025-06-23
- Description: Core: extract blendfile_header.py as common utility for parsing .blend files
- Author: Jacques Lucke
- [View on GitHub](https://github.com/blender/blender/commit/f0c7e52ff283f7b0832913b61c3e4b72bb5de764)

**c8997d821e173a0013d7429291bbff5b19655a53**
- Date: 2025-06-23
- Description: Refactor: Nodes: add InferenceValue utility class for socket usage inferencing
- Author: Jacques Lucke
- [View on GitHub](https://github.com/blender/blender/commit/c8997d821e173a0013d7429291bbff5b19655a53)

**3bc41b4175a640cc5488123fa1d07c6ff57b72a7**
- Date: 2025-06-23
- Description: Fix: File browser "asset" context query not ensuring file is asset
- Author: Julian Eisel
- [View on GitHub](https://github.com/blender/blender/commit/3bc41b4175a640cc5488123fa1d07c6ff57b72a7)

**1045a1b9105a2278a4cc935832a3fa2322bbad21**
- Date: 2025-06-23
- Description: Cleanup: Simplify context asset query logic
- Author: Julian Eisel
- [View on GitHub](https://github.com/blender/blender/commit/1045a1b9105a2278a4cc935832a3fa2322bbad21)

**7519586ec09990160f419c0226fdf0f66885bd85**
- Date: 2025-06-23
- Description: Fix #140693: Crash on enabling override in file output color settings
- Author: Sergey Sharybin
- [View on GitHub](https://github.com/blender/blender/commit/7519586ec09990160f419c0226fdf0f66885bd85)

**08151e623fc6be54c89e150de36639619b70c152**
- Date: 2025-06-23
- Description: Merge branch 'blender-v4.5-release'
- Author: Sergey Sharybin
- [View on GitHub](https://github.com/blender/blender/commit/08151e623fc6be54c89e150de36639619b70c152)

**0e304c813ca6bbfa8ca8b33ae506888f59b4b22c**
- Date: 2025-06-23
- Description: Refactor: Use Float2 images internally if possible
- Author: Omar Emara
- [View on GitHub](https://github.com/blender/blender/commit/0e304c813ca6bbfa8ca8b33ae506888f59b4b22c)

**e852533ddd3eecfd18c244fe682aa87579a48e26**
- Date: 2025-06-23
- Description: Fix #140781: Crash on "Adjust Pose asset" from Asset Browser
- Author: Julian Eisel
- [View on GitHub](https://github.com/blender/blender/commit/e852533ddd3eecfd18c244fe682aa87579a48e26)

**0c25411fd9d86afe8672861faa24d47ba4fcc2d9**
- Date: 2025-06-23
- Description: Merge branch 'blender-v4.5-release'
- Author: Julian Eisel
- [View on GitHub](https://github.com/blender/blender/commit/0c25411fd9d86afe8672861faa24d47ba4fcc2d9)

**a59a70edf4c058f9f780742425de4c5d8a300a90**
- Date: 2025-06-23
- Description: Curves: Support cyclic curves in Proportional Editing
- Author: илья _
- [View on GitHub](https://github.com/blender/blender/commit/a59a70edf4c058f9f780742425de4c5d8a300a90)

**c8ba4415c999ea8c67db56e8c848865406171514**
- Date: 2025-06-23
- Description: Build: Update linux libraries
- Author: Sebastian Parborg
- [View on GitHub](https://github.com/blender/blender/commit/c8ba4415c999ea8c67db56e8c848865406171514)

**e9bb088f777d89b22bf30eb76a6224190ebdd392**
- Date: 2025-06-23
- Description: Build: Update linux libraries
- Author: Sebastian Parborg
- [View on GitHub](https://github.com/blender/blender/commit/e9bb088f777d89b22bf30eb76a6224190ebdd392)

**f42b470902c720565a50523ca598acf47b7f1d65**
- Date: 2025-06-23
- Description: Build: Update linux libs
- Author: Sebastian Parborg
- [View on GitHub](https://github.com/blender/blender/commit/f42b470902c720565a50523ca598acf47b7f1d65)

**a7bcea76d7ff5440750c78e2ef2ae9592fff4e77**
- Date: 2025-06-23
- Description: Fix #139769: ACES 2.0 configuration fails with shader errors
- Author: Brecht Van Lommel
- [View on GitHub](https://github.com/blender/blender/commit/a7bcea76d7ff5440750c78e2ef2ae9592fff4e77)

**ef3e30c8fedc659edc026873d798e9b9dbe31479**
- Date: 2025-06-23
- Description: Merge branch 'blender-v4.5-release'
- Author: Sebastian Parborg
- [View on GitHub](https://github.com/blender/blender/commit/ef3e30c8fedc659edc026873d798e9b9dbe31479)

**13068b2e9cfd2d9fe0d798934eb86cbcee5608cf**
- Date: 2025-06-23
- Description: RNA: Add 'self as RNA' flag for RNA functions.
- Author: Bastien Montagne
- [View on GitHub](https://github.com/blender/blender/commit/13068b2e9cfd2d9fe0d798934eb86cbcee5608cf)

**5f00f9e08af782cc000aeaba89f86930021f1f64**
- Date: 2025-06-23
- Description: Fix #140381: Crash after creating a node group and undo
- Author: Habib Gahbiche
- [View on GitHub](https://github.com/blender/blender/commit/5f00f9e08af782cc000aeaba89f86930021f1f64)

**0e6dee37fac741a577a116b74038bd7caced5a90**
- Date: 2025-06-23
- Description: Fix: DRW: Assert caused by empty strand buf
- Author: Clément Foucault
- [View on GitHub](https://github.com/blender/blender/commit/0e6dee37fac741a577a116b74038bd7caced5a90)

**32d8860316f6bb696d7de738e8f1535348f07a3d**
- Date: 2025-06-23
- Description: Merge branch 'blender-v4.5-release'
- Author: Philipp Oeser
- [View on GitHub](https://github.com/blender/blender/commit/32d8860316f6bb696d7de738e8f1535348f07a3d)

**5790ff3b6aa92642d461666ba07478700e28a94b**
- Date: 2025-06-23
- Description: Fix #140801: Particle Weight Overlay Broken
- Author: Philipp Oeser
- [View on GitHub](https://github.com/blender/blender/commit/5790ff3b6aa92642d461666ba07478700e28a94b)

**c3ac501fa0190d61c4f6cf39b308cf1f44660558**
- Date: 2025-06-23
- Description: Merge branch 'blender-v4.5-release'
- Author: Philipp Oeser
- [View on GitHub](https://github.com/blender/blender/commit/c3ac501fa0190d61c4f6cf39b308cf1f44660558)

**ffcbe6205b970ca89625d1aa92afe9d8cf03ad48**
- Date: 2025-06-23
- Description: BLI: Add assert for `indexed_data_equal`
- Author: Sean Kim
- [View on GitHub](https://github.com/blender/blender/commit/ffcbe6205b970ca89625d1aa92afe9d8cf03ad48)

********************************************
- Date: 2025-06-23
- Description: Merge branch 'blender-v4.5-release'
- Author: Sean Kim
- [View on GitHub](https://github.com/blender/blender/commit/****************************************)

**a33881286a5a10a90ed29a85e3c6d57c41b36edd**
- Date: 2025-06-23
- Description: Cleanup: extract function determining whether socket is always single
- Author: Jacques Lucke
- [View on GitHub](https://github.com/blender/blender/commit/a33881286a5a10a90ed29a85e3c6d57c41b36edd)

**8dd3bc48cb0108ed58234e8b45f5bbb74e838a4f**
- Date: 2025-06-23
- Description: Animation: New test for armature deformation
- Author: Lukas Tönne
- [View on GitHub](https://github.com/blender/blender/commit/8dd3bc48cb0108ed58234e8b45f5bbb74e838a4f)

**c2356b6f27ac723b0441c37d6e4aebd8247db9ee**
- Date: 2025-06-23
- Description: Fix: Math node from old file is undefined
- Author: Omar Emara
- [View on GitHub](https://github.com/blender/blender/commit/c2356b6f27ac723b0441c37d6e4aebd8247db9ee)

**7276b2009a8819619263a1b897389662307b0ee0**
- Date: 2025-06-23
- Description: Core: Add new 'system IDprops' storage for runtime-generated RNA properties.
- Author: Bastien Montagne
- [View on GitHub](https://github.com/blender/blender/commit/7276b2009a8819619263a1b897389662307b0ee0)

**d600b1002a8984a1ac9c458cf4d096961840ece8**
- Date: 2025-06-23
- Description: Refactor: UI: Add uiLayout use_property_split methods
- Author: Guillermo Venegas
- [View on GitHub](https://github.com/blender/blender/commit/d600b1002a8984a1ac9c458cf4d096961840ece8)

**41d8066b1ed3198a6c481b109a9cd1f456cd8b40**
- Date: 2025-06-23
- Description: Fix: Potential NaN when calculating average values
- Author: Sean Kim
- [View on GitHub](https://github.com/blender/blender/commit/41d8066b1ed3198a6c481b109a9cd1f456cd8b40)

**7274fdb377ba87d1b71787485c5645a341e77c4e**
- Date: 2025-06-23
- Description: Fix #140556: Mask filter operations behave incorrectly on dense meshes
- Author: Sean Kim
- [View on GitHub](https://github.com/blender/blender/commit/7274fdb377ba87d1b71787485c5645a341e77c4e)

**bf483ae2eb01fe3088ac30972ad2859338cbcb2f**
- Date: 2025-06-23
- Description: Merge branch 'blender-v4.5-release'
- Author: Sean Kim
- [View on GitHub](https://github.com/blender/blender/commit/bf483ae2eb01fe3088ac30972ad2859338cbcb2f)

**8219e2202f9e06d1776a58a499b5e011439c9cbe**
- Date: 2025-06-23
- Description: Cleanup: USD: Various non-functional changes for usd_hook.cc
- Author: Jesse Yurkovich
- [View on GitHub](https://github.com/blender/blender/commit/8219e2202f9e06d1776a58a499b5e011439c9cbe)

**2cc5787885415b2e2c85778c28d7cf5d7488b40a**
- Date: 2025-06-23
- Description: Fix #67700: EEVEE: Particle Viewport Display affects Render result
- Author: Clément Foucault
- [View on GitHub](https://github.com/blender/blender/commit/2cc5787885415b2e2c85778c28d7cf5d7488b40a)

**82534672d4cac41b678e9fc2b5da7ea35713b894**
- Date: 2025-06-23
- Description: Fix broken Node Tools after IDProps storage split.
- Author: Bastien Montagne
- [View on GitHub](https://github.com/blender/blender/commit/82534672d4cac41b678e9fc2b5da7ea35713b894)

**379fffaf0da0b03156a451c8306730a24ae41239**
- Date: 2025-06-23
- Description: Fix #139664: bevel weight is now used for offset collisions #140436.
- Author: Rob Blair
- [View on GitHub](https://github.com/blender/blender/commit/379fffaf0da0b03156a451c8306730a24ae41239)

**ab96ffadb9454173c4fe2cc0fd82201c7cf0722b**
- Date: 2025-06-23
- Description: Merge branch 'blender-v4.5-release'
- Author: Howard Trickey
- [View on GitHub](https://github.com/blender/blender/commit/ab96ffadb9454173c4fe2cc0fd82201c7cf0722b)

**23da20bbc0c28648d70da0bce8370a3dcf9047dd**
- Date: 2025-06-23
- Description: Merge branch 'blender-v4.5-release'
- Author: Hans Goudey
- [View on GitHub](https://github.com/blender/blender/commit/23da20bbc0c28648d70da0bce8370a3dcf9047dd)
